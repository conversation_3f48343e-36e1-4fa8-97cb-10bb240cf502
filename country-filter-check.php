<?php
// Load WordPress
require_once 'wp-load.php';

// Check if WPForms is active
if (!defined('WPFORMS_VERSION')) {
    echo "WPForms is not active\n";
    exit;
}

echo "WPForms version: " . WPFORMS_VERSION . "\n";

// Check if Pro version is active
echo "WPForms Pro: " . (defined('WPFORMS_PRO') && WPFORMS_PRO ? 'Yes' : 'No') . "\n";

// Check if Country Filter class exists
$country_filter_class = 'WPForms\Pro\AntiSpam\CountryFilter';
echo "Country Filter class exists: " . (class_exists($country_filter_class) ? 'Yes' : 'No') . "\n";

// Check if any forms have Country Filter enabled
$forms = wpforms()->get('form')->get();
$forms_with_country_filter = 0;

if (!empty($forms)) {
    foreach ($forms as $form) {
        $form_data = wpforms_decode($form->post_content);
        
        if (
            !empty($form_data['settings']['anti_spam']['country_filter']['enable']) && 
            $form_data['settings']['anti_spam']['country_filter']['enable'] == '1'
        ) {
            $forms_with_country_filter++;
        }
    }
    
    echo "Forms with Country Filter enabled: {$forms_with_country_filter} out of " . count($forms) . "\n";
} else {
    echo "No forms found\n";
}

// Check if the class is properly hooked
global $wp_filter;
$process_hook_exists = false;

if (isset($wp_filter['wpforms_process_initial_errors'])) {
    foreach ($wp_filter['wpforms_process_initial_errors']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && is_object($callback['function'][0]) && get_class($callback['function'][0]) === $country_filter_class) {
                $process_hook_exists = true;
                break 2;
            }
        }
    }
}

echo "Country Filter process hook registered: " . ($process_hook_exists ? 'Yes' : 'No') . "\n";

// Check if GeoIP functionality works
$country_filter = new $country_filter_class();
$reflection = new ReflectionClass($country_filter);

$method = $reflection->getMethod('get_country_code');
$method->setAccessible(true);

echo "Current IP: " . wpforms_get_ip() . "\n";
echo "Detected country code: " . $method->invoke($country_filter) . "\n";
