<?php
/**
 * WebHook listener - created for WPForms e2e tests suite.
 * Outputs any request received in a file named webhook-result.html.
 */

header( "refresh:0;url=webhook-listener.php" );
$data   = file_get_contents( 'php://input' );
$events = json_decode( $data, true );

/**
 * Gets HTTP request headers.
 *
 * @return array
 */
function get_http_request_headers() {

	$http_headers = [];

	foreach ( $_SERVER as $key => $value ) {
		if ( substr( $key, 0, 5 ) !== 'HTTP_' ) {
			continue;
		}

		$single_header                  = str_replace( ' ', '-', ucwords( str_replace( '_', ' ', strtolower( substr( $key, 5 ) ) ) ) );
		$http_headers[ $single_header ] = $value;
	}

	return $http_headers;
}

if ( $events ) {
	file_put_contents( 'webhook-result.html', '<html>' ); // phpcs:ignore

	foreach ( $events as $key => $event ) {
		file_put_contents( 'webhook-result.html', $key . ' => ' . $event, FILE_APPEND ); // phpcs:ignore
		file_put_contents( 'webhook-result.html', '<br/>', FILE_APPEND ); // phpcs:ignore
	}

	$headers = get_http_request_headers();

	foreach ( $headers as $key => $value ) {
		file_put_contents( 'webhook-result.html', "$key => $value <br/>", FILE_APPEND ); // phpcs:ignore
	}

	file_put_contents( 'webhook-result.html', '</html>', FILE_APPEND ); // phpcs:ignore
}
