# WPForms ↔ n8n Integration (Addon + n8n Trigger node)

## Summary

**Goal:** Deliver a first‑class, zero‑friction bridge between WPForms and n8n using a **Webhook-like** integration in WPForms and a **single n8n WPForms Community Node (Trigger)** 

**End result for users:**

* From the WPForms **Marketing** tab, users enable “n8n Integration,” paste a webhook URL from n8n, map fields (including Smart Tags), optionally set Conditional Logic, and click **Save**.
* Every form submission is shipped to n8n **reliably and securely** (HMAC‑signed, timestamped) with minimal configuration.
* In n8n, the **WPForms (Trigger)** node validates the signature, parses the payload (fields, meta, files), and makes the data immediately available to downstream nodes (e.g., CRM, Sheets, Slack, email).

This pairing creates an out‑of‑the‑box automation path for WPForms customers to orchestrate actions across hundreds of services without writing code.

---

## Benefits

Describe the benefits of the rock/feature. How does it help our users and relate to our core values?

1. **Simplicity & Speed** — A familiar WPForms marketing‑integrations panel with field mapping and Conditional Logic gets users live in minutes.
2. **Reliability** — Background queue with retries (Action Scheduler), idempotency keys, and observable logs reduce data loss and duplicates.
3. **Security** — HMAC‑SHA256 signatures, timestamps (anti‑replay), selective field inclusion, and masked secrets in logs protect PII.
4. **Scalability** — n8n workflows can fan out to CRMs, data warehouses, email tools, and custom APIs without additional WP plugins.
5. **Compatibility & Extensibility** — Works with core WPForms features (Smart Tags, Conditional Logic, File Uploads, AntiSpam). The trigger node cleanly models WPForms’ payload so downstream steps are predictable.
6. **Ownership** — Supports self‑hosted n8n instances for customers who need data residency and privacy controls.

---

## Risks

* **Misconfiguration:** Wrong webhook URL or secret causes 401s; need clear validation and error UX.
* **n8n Downtime/Latency:** Remote endpoint failures could queue many pending jobs; must cap retries and expose DLQ (dead‑letter queue) visibility.
* **Large Files/Attachments:** Oversized uploads or slow links can exceed timeouts
* **Spam/Abuse Traffic:** If anti‑spam is bypassed, noisy submissions could pressure queues; Conditional Logic and spam status checks should gate dispatch.
* **GDPR/PII Handling:** Users may transmit sensitive data; provide clear docs, field‑level exclusions, and hashing options for specific fields (e.g., phone).
* **Version Drift:** n8n node updates vs. addon payload changes require semantic versioning and a stable contract.

---

## Addon Compatibility

**Expected:** No code changes required in addons. If “send after payment” is enabled, we’ll hook into existing payment completion actions.

---

## Technical Overview


### WPForms Addon

**Settings UI (Builder → Marketing → n8n Integration):**

Support multiple connections. Similar to WPForms Make addon. 

* **Enable toggle**
  * Enables the integration.

When enabled the default connection settings block appears:

* **Send on event**
  * Dropdown field:
    * Form Submitted
    * Entry Created
    * Entry rejected by AntiSpam
    * Payment Completed
    * ??
     
* **Webhook URL** (required)
  * Text field to copy-paste the webhook URL from n8n.
  * **Check connection** button (or icon) next to the URL field to validate the URL.
    * If valid, a green checkmark appears.
    * If invalid, a red exclamation mark appears next to the URL field.

* **Field Mapping** — map form fields/Smart Tags to JSON body keys.
  * Standard WPForms field mapping UI with Custom values and Smart Tags.
    
* **Conditional Logic** — reuse WPForms’ conditional editor.
  * Standard WPForms conditional logic UI.
  
* **Test Send** — sends a sample payload to the provided URL. 
  * Shows the HTTP status & response snippet in a standard modal. (need text/design)
  * This can be standardized to be re-used in other integrations like Webhooks, Make, etc. addons. 
  
**Dispatch Flow:**

1. On event (selected in the Send on Event dropdown), create and enqueue the new **Action Scheduler** task.
2. The task builds payload:
   * `form` (id, name), `entry` (id, createdAt), `fields` (resolved values), `meta` (ip, referrer, utm, page), `files` (array of `{name, size, mime, url}`)
   * Add filter to be able to modify the payload programmatically 

4. Compute headers:
```json
{
  "X-WPForms-Signature": "sha256=<HMAC(body, secret)>",
  "X-WPForms-Timestamp": "<ISO8601>",
  "X-WPForms-Idempotency": "<entryId-hash>"
}
```
   
4. Send via `wp_remote_post()` with sane timeouts;
   * Use HTTP POST method
   * Sane timeouts (up to 30sec)
   * Backoff on 5xx/network error with delay (1min)
     * Add filter to be able to be able to configure backoff delay value
   * Max attempts N (up to 5 by default)
     * Add filter to be able to be able to configure max attempts value

   
5. Log each attempt (status, duration, response up to N chars).

**Files Handling:**

* Prefer direct URLs if publicly accessible.
* Option to **base64 encoded** attachments included in the payload JSON.

**Payload Contract (example):**

```json
{
  "form": {
    "id": 123,
    "name": "Contact Us"
  },
  "entry": {
    "id": 4567,
    "createdAt": "2025-08-28T11:23:45Z"
  },
  "fields": {
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "message": "Hi! I'd like to learn more about your services.",
    "subscribe": true,
    "interests": ["Forms", "Automation"],
    "rating": 5,
    "appointment": "2025-09-05T14:30:00Z"
  },
  "meta": {
    "ip": "************",
    "referrer": "https://example.com/landing",
    "utm": {
      "source": "google",
      "medium": "cpc",
      "campaign": "summer_sale",
      "term": "wpforms",
      "content": "cta_button"
    },
    "page": {
      "url": "https://example.com/contact",
      "title": "Contact Us",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
      "language": "en-US"
    }
  },
  "files": [
    {
      "name": "resume.pdf",
      "size": 58231,
      "mime": "application/pdf",
      "url": "https://cdn.example.com/uploads/2025/08/resume.pdf"
    },
  ]
}
```

### n8n Community Node (Trigger: “WPForms”)



* **Credentials:** *WPForms HMAC Verify* (secret), optional timestamp skew.
* **Webhook URL:** Node registers an n8n webhook and displays it to copy into WPForms.
* **Validation:** Verifies `X-WPForms-Signature` and `X-WPForms-Timestamp` (reject if missing/skewed/signature mismatch). Optional **Idempotency** cache.
* **Output:** 1 item per submission with `form`, `entry`, `fields`, `files`, `meta` as above; convenience flattening and auto‑parsing of numbers/dates optional via toggles.
* **Quality‑of‑life:** options to fetch attachment bytes (on demand) and to split multi‑value fields into multiple items.

---

## Security

* **HMAC‑SHA256 signature** of the exact request body with a user‑provided secret.
* **Timestamp header** and ±5‑minute default skew to mitigate replay.
* **Idempotency key** to prevent duplicates on retries.
* **Least‑privilege UI:** role caps to view/edit integration settings.
* **Logs hygiene:** mask secrets; truncate large bodies.

---

## Challenges and Hurdles

* **Attachment delivery:** Implementing signed URLs that work across hosting setups/CDNs.
* **Dispatch timing with payments:** Coordinating send‑after‑payment vs. immediate send across gateways.
* **Queue pressure & backoff tuning:** Picking defaults that balance reliability and timeliness.
* **Self‑hosted n8n variability:** TLS/cert issues, reverse proxies, rate limits, or auth challenges on user servers.
* **Internationalization & Formatting:** Normalizing dates, currency/decimal separators, phone formats.
* **DX/UX polish:** Making Test Send errors actionable; surfacing misconfig states clearly in Builder.

---

## Tasks and Time Estimates

*(Rough, engineering days; includes unit/integration tests where relevant)*


---
**Assumptions:**

* Reuse existing Conditional Logic editor; no new UI component there.
* Payments “send after success” hook available in Stripe/PayPal addons.
* Signed URLs implementable on supported hosting; otherwise fallback to direct/public URLs.

**Out of Scope (v1):**

* API‑driven workflow provisioning in n8n
* Bi‑directional lookups/enrichment
* Inline Base64 file streaming for very large attachments
