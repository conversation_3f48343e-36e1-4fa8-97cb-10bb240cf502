// eslint-disable-next-line no-unused-vars
/* global wpforms, wpforms_settings */

/**
 * Main JS file for the addon.
 *
 * @since 1.0.0
 */
const WPFormsN8n = window.WPFormsN8n || ( function( document, window, $ ) {
	/**
	 * Public functions and properties.
	 *
	 * @since 1.0.0
	 *
	 * @type {Object}
	 */
	const app = {

		/**
		 * Start the engine.
		 *
		 * @since 1.0.0
		 */
		init() {
			$( app.ready );
		},

		/**
		 * Document ready.
		 *
		 * @since 1.0.0
		 */
		ready() {
			app.bindEvents();
		},

		/**
		 * Bind events.
		 *
		 * @since 1.0.0
		 */
		bindEvents() {},
	};

	return app;
}( document, window, jQuery ) );

WPFormsN8n.init();
