$content = Get-Content -Path "wp-content\plugins\wpforms-qr-code\plan.md" -Raw

# Remove all code blocks (text between ```php and ```)
$content = $content -replace "(?s)- \[x\] Implementation example:\s*```php.*?```", "- [x] Implementation complete"

# Save the modified content back to the file
Set-Content -Path "wp-content\plugins\wpforms-qr-code\plan.md" -Value $content

Write-Host "Code examples have been removed from plan.md"
