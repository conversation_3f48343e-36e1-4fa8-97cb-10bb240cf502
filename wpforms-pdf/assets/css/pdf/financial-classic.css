body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: bold;
  margin: 0 0 10px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 10px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

/* Base */
.body-inner {
  padding-top: 50px;
  padding-bottom: 50px;
}

.wrapper {
  max-width: 700px;
}

.wrapper-inner {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 40px 50px 50px 50px;
}

.header {
  text-align: center;
  padding: 0 0 50px 0;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  padding-top: 10px;
  font-size: 14px;
  line-height: 24px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 24px;
}

/* Tables */
.content .field-name {
  padding-top: 10px;
  padding-bottom: 10px;
  /* Repeater & Layout */
}

.content .field-name:not(.field-value) {
  font-size: 18px;
  line-height: 20.7px;
}

.content .field-name.field-repeater-name, .content .field-name.field-layout-name {
  font-size: 22px;
  padding-top: 30px;
  padding-bottom: 30px;
}

.content .field-value {
  padding-bottom: 30px;
}

.content .field-name.field-value {
  line-height: 24px;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.business-info {
  margin-top: 20px;
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
}

.business-info td {
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px !important;
  padding: 0;
  text-align: center !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.business-info td.divider {
  padding: 0;
  margin: 0;
  background: none !important;
  width: 0;
  opacity: 0.25;
}

.business-info td.business_address {
  padding-left: 25px;
  padding-right: 10px;
  max-width: 53%;
  width: 53%;
}

.business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
  max-width: 35%;
  width: 35%;
}

.business-info td.business_phone {
  padding-left: 10px;
  padding-right: 25px;
  max-width: 20%;
  width: 20%;
}

.header table {
  width: 100%;
}

.header table td {
  text-align: left;
}

.header table tr.divider {
  opacity: 1;
  margin: 0;
  background: none;
}

.header table tr.divider td {
  padding: 40px 0 40px 0;
  margin: 0;
}

.header table tr.divider .divider {
  margin: 0;
}

.logo-container[data-align="left"] {
  text-align: left;
}

.logo-container[data-align="center"] {
  text-align: center;
}

.logo-container[data-align="right"] {
  text-align: right;
}

.heading_1 {
  font-family: "Literata", serif;
  font-size: 48px;
  line-height: 36px;
  text-transform: uppercase;
  vertical-align: middle;
  color: #44403C;
}

.container-content {
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.subheading {
  font-family: "Literata", serif;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 2px;
  margin: 0 0 5px 0;
  text-transform: uppercase;
  padding-right: 40px;
  color: #FA7315;
}

.subcontent {
  font-size: 13px;
  line-height: 18px;
  padding-right: 40px;
  color: #78716C;
}

.subcontent p, .subcontent b, .subcontent strong, .subcontent div, .subcontent span, .subcontent a {
  font-size: 13px;
  line-height: 18px;
  color: #78716C;
  margin: 0;
  text-decoration: none;
}

.subcontent b, .subcontent strong {
  font-weight: bold;
  color: #44403C;
}

.wpforms-order-summary-preview th, .wpforms-order-summary-preview td {
  font-size: 14px;
  font-weight: normal;
  line-height: 14px;
  padding: 15px 20px;
  border-top: 2px solid #E7E5E4;
  border-bottom: 1px solid #E7E5E4;
}

.wpforms-order-summary-preview th {
  font-weight: bold;
}

.wpforms-order-summary-preview td {
  border-top-width: 1px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-label {
  text-align: left;
  padding-left: 0;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity {
  width: 120px;
  text-align: center;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-full {
  font-size: 14px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-price {
  width: 80px;
  text-align: left;
  padding-right: 0;
}

.wpforms-order-summary-preview .wpforms-order-summary-preview-total td {
  border-bottom-width: 0;
  font-weight: bold;
}

.logo-container[data-size="small"] img.logo {
  max-height: 70px;
  max-width: 40%;
}

.logo-container[data-size="medium"] img.logo {
  max-height: 120px;
  max-width: 60%;
}

.logo-container[data-size="large"] img.logo {
  max-height: 170px;
  max-width: 80%;
}

.logo-container[data-size="full"] img.logo {
  max-height: 260px;
  max-width: 100%;
}

.header {
  margin-bottom: 40px;
}

.header table td.logo {
  max-width: 50% !important;
  width: 50% !important;
}

.header table td.heading_1 {
  text-align: right;
  max-width: 50%;
  width: 50%;
  white-space: normal;
  line-height: 36px;
  font-weight: 700;
}

.subheading {
  line-height: 20px;
}

.wpforms-order-summary-placeholder td {
  padding-left: 0;
}

.billing-info,
.terms-info {
  padding-right: 40px;
  width: 60%;
}

.payment_heading,
.payment_content {
  padding-right: 0;
}

.wpforms-order-summary-preview .wpforms-order-summary-preview-total td {
  font-family: "Literata", serif;
}

.preview .container-content {
  height: calc( 100vh - 270px);
  overflow: hidden;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
