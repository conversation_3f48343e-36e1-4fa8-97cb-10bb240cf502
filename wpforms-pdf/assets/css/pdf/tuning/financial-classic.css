.container-background .container-background-fill {
  bottom: 45px;
}

.container-background .container-shadow[data-shadow="small"] {
  right: -4px;
  bottom: 37px;
}

.container-background .container-shadow[data-shadow="medium"] {
  right: -8px;
  bottom: 27px;
}

.container-background .container-shadow[data-shadow="large"] {
  right: -18px;
  bottom: 12px;
}

.container-content .header .business-info {
  margin-left: -10px;
  margin-right: -10px;
}

.container-content .header .business-info td.business_address {
  padding-left: 15px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_phone {
  padding-left: 10px;
  padding-right: 15px;
  max-width: 25%;
  width: 21%;
}

.container-content .content table.body, .container-content .content h1, .container-content .content h2, .container-content .content h3, .container-content .content h4, .container-content .content h5, .container-content .content h6, .container-content .content p, .container-content .content td:not(.header), .container-content .content th, .container-content .content a {
  line-height: 1;
}

.container-content .content > table tr td.field-name {
  padding-top: 10px !important;
}

.container-content .content > table tr td.field-value {
  padding: 5px 15px 10px 0px !important;
}

.portrait.A4 .container-content > .divider {
  margin-top: 50px;
  margin-bottom: 50px;
}

.portrait.letter .container-content > .divider {
  margin-top: 40px;
  margin-bottom: 40px;
}

.header table tr td.billing-info {
  width: 60%;
}

.footer table tr td.terms-info {
  width: 60%;
}

.subheading {
  line-height: 14px;
  margin: 0 0 2px 0;
  padding-right: 0;
}

.subcontent {
  padding-right: 0;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
