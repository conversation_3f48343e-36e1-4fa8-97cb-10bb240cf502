.container-background .container-background-fill {
  bottom: 45px;
}

.container-background .container-shadow[data-shadow="small"] {
  right: -4px;
  bottom: 37px;
}

.container-background .container-shadow[data-shadow="medium"] {
  right: -8px;
  bottom: 27px;
}

.container-background .container-shadow[data-shadow="large"] {
  right: -18px;
  bottom: 12px;
}

.container-content .header .business-info {
  margin-left: -10px;
  margin-right: -10px;
}

.container-content .header .business-info td.business_address {
  padding-left: 15px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_phone {
  padding-left: 10px;
  padding-right: 15px;
  max-width: 25%;
  width: 21%;
}

.container-content .content table.body, .container-content .content h1, .container-content .content h2, .container-content .content h3, .container-content .content h4, .container-content .content h5, .container-content .content h6, .container-content .content p, .container-content .content td:not(.header), .container-content .content th, .container-content .content a {
  line-height: 1;
}

.container-content .content > table tr td.field-name {
  padding-top: 10px !important;
}

.container-content .content > table tr td.field-value {
  padding: 5px 15px 10px 0px !important;
}

.portrait.A4 .container-content > .divider {
  margin-top: 50px;
  margin-bottom: 50px;
}

.portrait.letter .container-content > .divider {
  margin-top: 40px;
  margin-bottom: 40px;
}

.container-content .heading-container h1.heading_1 {
  margin-bottom: 0;
}

.container-content .heading-container h2.heading_2 {
  margin-top: -10px;
}

.container-content h3.heading_3 {
  margin-top: -20px;
}

.container-content .cname {
  font-family: "Literata", serif;
  margin: -12px 0 0 0;
}

.container-content .content {
  margin: 15px 0 0 0;
  line-height: 15px;
}

.container-content .content p, .container-content .content b, .container-content .content strong, .container-content .content div, .container-content .content span, .container-content .content a {
  line-height: 15px;
}

.container-content .footer {
  margin-top: -20px;
  margin-bottom: -20px;
}

.container-content .footer .badge {
  margin-left: 3px;
}

.container-content .footer .signature .signature_text {
  line-height: 35px;
}

.landscape .container-content .header {
  height: 150px;
}

.landscape .container-content .header .logo-container img {
  margin-top: 13px;
}

.landscape .container-content .heading-container {
  margin-top: -2px;
}

.landscape .container-content .heading-container h1.heading_1 {
  margin-top: -20px;
}

.landscape .container-content .heading-container h2.heading_2 {
  margin-top: -20px;
}

.landscape .container-content h3.heading_3 {
  margin-top: -25px;
}

.landscape .container-content .divider {
  margin-top: 34px;
  margin-bottom: 34px;
}

.landscape .container-content .divider.bottom {
  margin: 15px 0 0 0;
}

.landscape .container-content .cname {
  margin: -12px 0 0 0;
}

.landscape .container-content .content {
  margin-top: 15px;
  margin-bottom: 0;
}

.landscape .container-content .content * {
  line-height: 14px;
}

.landscape .container-content .footer {
  margin-top: -20px;
  margin-bottom: -20px;
}

.landscape .container-content .footer .divider {
  margin: 6px 0;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
