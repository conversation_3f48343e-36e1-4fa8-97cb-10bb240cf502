.container-background .container-background-fill {
  bottom: 45px;
}

.container-background .container-shadow[data-shadow="small"] {
  right: -4px;
  bottom: 37px;
}

.container-background .container-shadow[data-shadow="medium"] {
  right: -8px;
  bottom: 27px;
}

.container-background .container-shadow[data-shadow="large"] {
  right: -18px;
  bottom: 12px;
}

.container-content .header .business-info {
  margin-left: -10px;
  margin-right: -10px;
}

.container-content .header .business-info td.business_address {
  padding-left: 15px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_phone {
  padding-left: 10px;
  padding-right: 15px;
  max-width: 25%;
  width: 21%;
}

.container-content .content table.body, .container-content .content h1, .container-content .content h2, .container-content .content h3, .container-content .content h4, .container-content .content h5, .container-content .content h6, .container-content .content p, .container-content .content td:not(.header), .container-content .content th, .container-content .content a {
  line-height: 1;
}

.container-content .content > table tr td.field-name {
  padding-top: 10px !important;
}

.container-content .content > table tr td.field-value {
  padding: 5px 15px 10px 0px !important;
}

.portrait.A4 .container-content > .divider {
  margin-top: 50px;
  margin-bottom: 50px;
}

.portrait.letter .container-content > .divider {
  margin-top: 40px;
  margin-bottom: 40px;
}

.container-content .heading-container h1.heading_1 {
  margin-bottom: 0;
}

.container-content .heading-container h2.heading_2 {
  margin-top: 0px;
  margin-bottom: -10px;
}

.container-content h3.heading_3 {
  margin-top: -10px;
}

.container-content .cname {
  margin: 0;
}

.container-content .content {
  margin: 15px 0 0 0;
  line-height: 18px;
}

.container-content .content p, .container-content .content b, .container-content .content strong, .container-content .content div, .container-content .content span, .container-content .content a {
  line-height: 18px;
}

.container-content .footer {
  margin-top: 0px;
  margin-bottom: -20px;
}

.container-content .footer .badge {
  margin-left: 10px;
}

.container-content .footer .signature .signature_text {
  line-height: 35px;
}

.landscape .container-content .header {
  height: 160px;
}

.landscape .container-content .header .logo {
  margin-top: 20px;
}

.landscape .container-content .heading-container h1.heading_1 {
  margin-top: -10px;
}

.landscape .container-content .heading-container h2.heading_2 {
  margin-top: -10px;
}

.landscape .container-content h3.heading_3 {
  margin-top: -4px;
}

.landscape .container-content .divider {
  margin-top: 28px;
  margin-bottom: 28px;
}

.landscape .container-content .divider.bottom {
  margin: 30px 0 0 0;
}

.landscape .container-content .cname {
  margin: -5px 0 0 0;
}

.landscape .container-content .content {
  margin: 16px 50px 0 50px;
}

.landscape .container-content .footer {
  margin-top: 0px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
