.container-background .container-background-fill {
  bottom: 45px;
}

.container-background .container-shadow[data-shadow="small"] {
  right: -4px;
  bottom: 37px;
}

.container-background .container-shadow[data-shadow="medium"] {
  right: -8px;
  bottom: 27px;
}

.container-background .container-shadow[data-shadow="large"] {
  right: -18px;
  bottom: 12px;
}

.container-content .header .business-info {
  margin-left: -10px;
  margin-right: -10px;
}

.container-content .header .business-info td.business_address {
  padding-left: 15px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
}

.container-content .header .business-info td.business_phone {
  padding-left: 10px;
  padding-right: 15px;
  max-width: 25%;
  width: 21%;
}

.container-content .content table.body, .container-content .content h1, .container-content .content h2, .container-content .content h3, .container-content .content h4, .container-content .content h5, .container-content .content h6, .container-content .content p, .container-content .content td:not(.header), .container-content .content th, .container-content .content a {
  line-height: 1;
}

.container-content .content > table tr td.field-name {
  padding-top: 10px !important;
}

.container-content .content > table tr td.field-value {
  padding: 5px 15px 10px 0px !important;
}

.portrait.A4 .container-content > .divider {
  margin-top: 50px;
  margin-bottom: 50px;
}

.portrait.letter .container-content > .divider {
  margin-top: 40px;
  margin-bottom: 40px;
}

.container-content .content > table tr td.field-name {
  padding-top: 10px !important;
}

.container-content .content > table tr td.field-value {
  padding: 5px 15px 10px 15px !important;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicGRmL3R1bmluZy9kb2N1bWVudC10YWJsZS5jc3MiLCJzb3VyY2VzIjpbInBkZi90dW5pbmcvZG9jdW1lbnQtdGFibGUuc2NzcyIsInBkZi90dW5pbmcvcGFydGlhbHMvX2RvY3VtZW50LnNjc3MiLCJwZGYvcGFydGlhbHMvX3ZhcnMuc2NzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBXUEZvcm1zIFBERiBzdHlsZXMuXG4vL1xuLy8gVHVuaW5nIHN0eWxlcyBmb3IgUERGIGdlbmVyYXRpb24uXG4vLyBUZW1wbGF0ZTogZG9jdW1lbnQtdGFibGVcbi8vXG4vLyBAc2luY2UgMS4wLjBcblxuLy8gQ29tbW9uIHBhcnQgZm9yIHR1bmluZyBhbGwgdGhlIHRlbXBsYXRlcy5cbkBpbXBvcnQgXCJwYXJ0aWFscy9kb2N1bWVudFwiO1xuXG4uY29udGFpbmVyLWNvbnRlbnQgLmNvbnRlbnQge1xuXHQmID4gdGFibGUge1xuXHRcdHRyIHtcblx0XHRcdHRkIHtcblx0XHRcdFx0Ji5maWVsZC1uYW1lIHtcblx0XHRcdFx0XHRwYWRkaW5nLXRvcDogMTBweCAhaW1wb3J0YW50O1xuXHRcdFx0XHR9XG5cblx0XHRcdFx0Ji5maWVsZC12YWx1ZSB7XG5cdFx0XHRcdFx0cGFkZGluZzogNXB4IDE1cHggMTBweCAxNXB4ICFpbXBvcnRhbnQ7XG5cdFx0XHRcdH1cblx0XHRcdH1cblx0XHR9XG5cdH1cbn1cbiIsIi8vIFdQRm9ybXMgUERGIHN0eWxlcy5cbi8vXG4vLyBUdW5pbmcgc3R5bGVzIGZvciBQREYgZ2VuZXJhdGlvbi5cbi8vIENhdGVnb3J5OiBkb2N1bWVudFxuLy9cbi8vIEBzaW5jZSAxLjAuMFxuXG5AaW1wb3J0IFwiLi4vcGFydGlhbHMvdmFyc1wiO1xuXG4uY29udGFpbmVyLWJhY2tncm91bmQge1xuXHQuY29udGFpbmVyLWJhY2tncm91bmQtZmlsbCB7XG5cdFx0Ym90dG9tOiAkYm9keV9tYXJnaW47XG5cdH1cblxuXHQuY29udGFpbmVyLXNoYWRvdyB7XG5cdFx0JltkYXRhLXNoYWRvdz1cInNtYWxsXCJdIHtcblx0XHRcdHJpZ2h0OiAtNHB4O1xuXHRcdFx0Ym90dG9tOiAjeyAkYm9keV9tYXJnaW4gLSA4cHggfTtcblx0XHR9XG5cblx0XHQmW2RhdGEtc2hhZG93PVwibWVkaXVtXCJdIHtcblx0XHRcdHJpZ2h0OiAtOHB4O1xuXHRcdFx0Ym90dG9tOiAjeyAkYm9keV9tYXJnaW4gLSAxOHB4IH07XG5cdFx0fVxuXG5cdFx0JltkYXRhLXNoYWRvdz1cImxhcmdlXCJdIHtcblx0XHRcdHJpZ2h0OiAtMThweDtcblx0XHRcdGJvdHRvbTogI3sgJGJvZHlfbWFyZ2luIC0gMzNweCB9O1xuXHRcdH1cblx0fVxufVxuXG4uY29udGFpbmVyLWNvbnRlbnQge1xuXHQuaGVhZGVyIHtcblx0XHQuYnVzaW5lc3MtaW5mbyB7XG5cdFx0XHRtYXJnaW4tbGVmdDogLTEwcHg7XG5cdFx0XHRtYXJnaW4tcmlnaHQ6IC0xMHB4O1xuXG5cdFx0XHR0ZCB7XG5cdFx0XHRcdCYuYnVzaW5lc3NfYWRkcmVzcyB7XG5cdFx0XHRcdFx0cGFkZGluZy1sZWZ0OiAxNXB4O1xuXHRcdFx0XHRcdHBhZGRpbmctcmlnaHQ6IDEwcHg7XG5cdFx0XHRcdH1cblxuXHRcdFx0XHQmLmJ1c2luZXNzX2VtYWlsIHtcblx0XHRcdFx0XHRwYWRkaW5nLWxlZnQ6IDEwcHg7XG5cdFx0XHRcdFx0cGFkZGluZy1yaWdodDogMTBweDtcblx0XHRcdFx0fVxuXG5cdFx0XHRcdCYuYnVzaW5lc3NfcGhvbmUge1xuXHRcdFx0XHRcdHBhZGRpbmctbGVmdDogMTBweDtcblx0XHRcdFx0XHRwYWRkaW5nLXJpZ2h0OiAxNXB4O1xuXHRcdFx0XHRcdG1heC13aWR0aDogMjUlO1xuXHRcdFx0XHRcdHdpZHRoOiAyMSU7XG5cdFx0XHRcdH1cblx0XHRcdH1cblx0XHR9XG5cdH1cblxuXHQuY29udGVudCB7XG5cdFx0dGFibGUuYm9keSwgaDEsIGgyLCBoMywgaDQsIGg1LCBoNiwgcCwgdGQ6bm90KC5oZWFkZXIpLCB0aCwgYSB7XG5cdFx0XHRsaW5lLWhlaWdodDogMTtcblx0XHR9XG5cblx0XHQmID4gdGFibGUge1xuXHRcdFx0dHIge1xuXHRcdFx0XHR0ZCB7XG5cdFx0XHRcdFx0Ji5maWVsZC1uYW1lIHtcblx0XHRcdFx0XHRcdHBhZGRpbmctdG9wOiAxMHB4ICFpbXBvcnRhbnQ7XG5cdFx0XHRcdFx0fVxuXG5cdFx0XHRcdFx0Ji5maWVsZC12YWx1ZSB7XG5cdFx0XHRcdFx0XHRwYWRkaW5nOiA1cHggMTVweCAxMHB4IDBweCAhaW1wb3J0YW50O1xuXHRcdFx0XHRcdH1cblx0XHRcdFx0fVxuXHRcdFx0fVxuXHRcdH1cblx0fVxufVxuXG4ucG9ydHJhaXQge1xuXHQmLkE0IHtcblx0XHQuY29udGFpbmVyLWNvbnRlbnQge1xuXHRcdFx0JiA+IC5kaXZpZGVyIHtcblx0XHRcdFx0bWFyZ2luLXRvcDogNTBweDtcblx0XHRcdFx0bWFyZ2luLWJvdHRvbTogNTBweDtcblx0XHRcdH1cblx0XHR9XG5cdH1cblxuXHQmLmxldHRlciB7XG5cdFx0LmNvbnRhaW5lci1jb250ZW50IHtcblx0XHRcdCYgPiAuZGl2aWRlciB7XG5cdFx0XHRcdG1hcmdpbi10b3A6IDQwcHg7XG5cdFx0XHRcdG1hcmdpbi1ib3R0b206IDQwcHg7XG5cdFx0XHR9XG5cdFx0fVxuXHR9XG59XG4iLCIvLyBXUEZvcm1zIFBERiBzdHlsZXMuXG4vL1xuLy8gVmFyaWFibGVzIGZvciBhbGwgdGVtcGxhdGVzLlxuLy9cbi8vIEBzaW5jZSAxLjAuMFxuXG4kYm9keV9tYXJnaW46IDQ1cHg7XG4kY29udGFpbmVyX3BhZGRpbmc6IDYwcHg7XG5cbiRmb250X3NlcmlmOiBcIkxpdGVyYXRhXCIsIHNlcmlmO1xuJGZvbnRfc2FuczogXCJJbnRlclwiLCBzYW5zLXNlcmlmO1xuJGZvbnRfY3Vyc2l2ZTogXCJMb2JzdGVyXCIsIGN1cnNpdmU7XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFDU0EsQUFDQyxxQkFEb0IsQ0FDcEIsMEJBQTBCLENBQUM7RUFDMUIsTUFBTSxFQ0xNLElBQUk7Q0RNaEI7O0FBSEYsQUFNRSxxQkFObUIsQ0FLcEIsaUJBQWlCLENBQ2YsQUFBQSxXQUFDLENBQVksT0FBTyxBQUFuQixFQUFxQjtFQUN0QixLQUFLLEVBQUUsSUFBSTtFQUNYLE1BQU0sRUFBQyxJQUFDO0NBQ1I7O0FBVEgsQUFXRSxxQkFYbUIsQ0FLcEIsaUJBQWlCLENBTWYsQUFBQSxXQUFDLENBQVksUUFBUSxBQUFwQixFQUFzQjtFQUN2QixLQUFLLEVBQUUsSUFBSTtFQUNYLE1BQU0sRUFBQyxJQUFDO0NBQ1I7O0FBZEgsQUFnQkUscUJBaEJtQixDQUtwQixpQkFBaUIsQ0FXZixBQUFBLFdBQUMsQ0FBWSxPQUFPLEFBQW5CLEVBQXFCO0VBQ3RCLEtBQUssRUFBRSxLQUFLO0VBQ1osTUFBTSxFQUFDLElBQUM7Q0FDUjs7QUFJSCxBQUVFLGtCQUZnQixDQUNqQixPQUFPLENBQ04sY0FBYyxDQUFDO0VBQ2QsV0FBVyxFQUFFLEtBQUs7RUFDbEIsWUFBWSxFQUFFLEtBQUs7Q0FvQm5COztBQXhCSCxBQU9JLGtCQVBjLENBQ2pCLE9BQU8sQ0FDTixjQUFjLENBSWIsRUFBRSxBQUNBLGlCQUFpQixDQUFDO0VBQ2xCLFlBQVksRUFBRSxJQUFJO0VBQ2xCLGFBQWEsRUFBRSxJQUFJO0NBQ25COztBQVZMLEFBWUksa0JBWmMsQ0FDakIsT0FBTyxDQUNOLGNBQWMsQ0FJYixFQUFFLEFBTUEsZUFBZSxDQUFDO0VBQ2hCLFlBQVksRUFBRSxJQUFJO0VBQ2xCLGFBQWEsRUFBRSxJQUFJO0NBQ25COztBQWZMLEFBaUJJLGtCQWpCYyxDQUNqQixPQUFPLENBQ04sY0FBYyxDQUliLEVBQUUsQUFXQSxlQUFlLENBQUM7RUFDaEIsWUFBWSxFQUFFLElBQUk7RUFDbEIsYUFBYSxFQUFFLElBQUk7RUFDbkIsU0FBUyxFQUFFLEdBQUc7RUFDZCxLQUFLLEVBQUUsR0FBRztDQUNWOztBQXRCTCxBQTRCRSxrQkE1QmdCLENBMkJqQixRQUFRLENBQ1AsS0FBSyxBQUFBLEtBQUssRUE1Qlosa0JBQWtCLENBMkJqQixRQUFRLENBQ0ssRUFBRSxFQTVCaEIsa0JBQWtCLENBMkJqQixRQUFRLENBQ1MsRUFBRSxFQTVCcEIsa0JBQWtCLENBMkJqQixRQUFRLENBQ2EsRUFBRSxFQTVCeEIsa0JBQWtCLENBMkJqQixRQUFRLENBQ2lCLEVBQUUsRUE1QjVCLGtCQUFrQixDQTJCakIsUUFBUSxDQUNxQixFQUFFLEVBNUJoQyxrQkFBa0IsQ0EyQmpCLFFBQVEsQ0FDeUIsRUFBRSxFQTVCcEMsa0JBQWtCLENBMkJqQixRQUFRLENBQzZCLENBQUMsRUE1QnZDLGtCQUFrQixDQTJCakIsUUFBUSxDQUNnQyxFQUFFLEFBQUEsSUFBSyxDQUFBLE9BQU8sR0E1QnZELGtCQUFrQixDQTJCakIsUUFBUSxDQUNpRCxFQUFFLEVBNUI1RCxrQkFBa0IsQ0EyQmpCLFFBQVEsQ0FDcUQsQ0FBQyxDQUFDO0VBQzdELFdBQVcsRUFBRSxDQUFDO0NBQ2Q7O0FBOUJILEFBbUNLLGtCQW5DYSxDQTJCakIsUUFBUSxHQUtILEtBQUssQ0FDUixFQUFFLENBQ0QsRUFBRSxBQUNBLFdBQVcsQ0FBQztFQUNaLFdBQVcsRUFBRSxlQUFlO0NBQzVCOztBQXJDTixBQXVDSyxrQkF2Q2EsQ0EyQmpCLFFBQVEsR0FLSCxLQUFLLENBQ1IsRUFBRSxDQUNELEVBQUUsQUFLQSxZQUFZLENBQUM7RUFDYixPQUFPLEVBQUUsNEJBQTRCO0NBQ3JDOztBQU9OLEFBR0csU0FITSxBQUNQLEdBQUcsQ0FDSCxrQkFBa0IsR0FDYixRQUFRLENBQUM7RUFDWixVQUFVLEVBQUUsSUFBSTtFQUNoQixhQUFhLEVBQUUsSUFBSTtDQUNuQjs7QUFOSixBQVlHLFNBWk0sQUFVUCxPQUFPLENBQ1Asa0JBQWtCLEdBQ2IsUUFBUSxDQUFDO0VBQ1osVUFBVSxFQUFFLElBQUk7RUFDaEIsYUFBYSxFQUFFLElBQUk7Q0FDbkI7O0FEckZKLEFBSUksa0JBSmMsQ0FBQyxRQUFRLEdBQ3RCLEtBQUssQ0FDUixFQUFFLENBQ0QsRUFBRSxBQUNBLFdBQVcsQ0FBQztFQUNaLFdBQVcsRUFBRSxlQUFlO0NBQzVCOztBQU5MLEFBUUksa0JBUmMsQ0FBQyxRQUFRLEdBQ3RCLEtBQUssQ0FDUixFQUFFLENBQ0QsRUFBRSxBQUtBLFlBQVksQ0FBQztFQUNiLE9BQU8sRUFBRSw2QkFBNkI7Q0FDdEMifQ== */
