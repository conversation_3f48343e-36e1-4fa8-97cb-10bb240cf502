body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  font-weight: bold;
  margin: 0 0 10px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 10px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

/* Base */
.body-inner {
  padding-top: 80px;
  padding-bottom: 80px;
}

.wrapper {
  max-width: 700px;
}

.wrapper-inner {
  background-color: #ffffff;
  padding: 30px 50px 30px 50px;
}

.header {
  text-align: center;
  padding-bottom: 30px;
  border-bottom: 1px solid #d9d9d9;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  font-size: 14px;
  padding-top: 30px;
  line-height: 24px;
  border-top: 1px solid #d9d9d9;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 24px;
}

body,
table.body,
p,
td,
th {
  color: #333333;
}

/* Content */
.content {
  padding-top: 35px;
  padding-bottom: 25px;
}

tr:first-child > .content {
  padding-top: 15px;
}

.content .field-name {
  color: #333333;
  padding-top: 15px;
  padding-bottom: 10px;
  /* Repeater & Layout */
}

.content .field-name:not(.field-value) {
  font-size: 18px;
  line-height: 19.8px;
}

.content .field-name.field-repeater-name, .content .field-name.field-layout-name {
  font-size: 22px;
  padding-top: 25px;
  padding-bottom: 25px;
}

.content .field-value {
  padding-bottom: 25px;
}

.content .field-name.field-value {
  line-height: 24px;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview {
  border-radius: 0;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.container-background-wrapper {
  border-radius: 0;
}

.container-background-wrapper > .header {
  visibility: hidden !important;
}

.container-background-wrapper .container-background-fill {
  top: 0;
  right: 0;
  left: 0;
  bottom: unset;
  border-radius: 0;
  padding: 50px;
}

.container-background-wrapper .container-background-fill > .header,
.container-background-wrapper .container-background-fill > .content,
.container-background-wrapper .container-background-fill > .footer {
  visibility: hidden !important;
}

.container-background-wrapper .container-shadow {
  bottom: unset;
}

.content table tbody tr:first-child td {
  padding-top: 0;
}

.divider-invisible {
  visibility: hidden;
  height: 0;
  max-height: 0;
}

body {
  margin-top: 45px;
}

.header {
  margin: 0 -50px 50px -50px;
  padding: 0 50px;
}

.header[data-logo=""]:has(img[src=""]) {
  margin-bottom: 0;
}

.header:has(img[src=""]) {
  margin-bottom: 0;
}

.container-content .content {
  margin-top: 50px;
}

.container-background-wrapper .container-background-fill {
  padding: 30px 50px;
}

.container-background {
  border-radius: 0;
  padding: 30px 50px 30px 50px;
}

.container-background .header {
  border-bottom: none;
}

.header {
  padding: 0 50px;
  margin-top: -30px;
}

.header .logo {
  margin-top: 30px;
  margin-bottom: 30px;
}

.header[data-logo=""], .header:has(img[src=""]) {
  border-bottom: none;
}

.header[data-logo=""] + .content, .header:has(img[src=""]) + .content {
  margin-top: 30px;
}

.container-content {
  margin: 0 45px;
  padding: 30px 50px 30px 50px;
}

.container-content .content {
  margin-top: 0;
}

.content {
  margin-top: 0;
  padding: 0;
}

.content .divider {
  margin-top: 20px;
}

.divider {
  margin: 0 -50px;
  width: auto;
}

.footer {
  border-top: none;
  text-align: center;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
