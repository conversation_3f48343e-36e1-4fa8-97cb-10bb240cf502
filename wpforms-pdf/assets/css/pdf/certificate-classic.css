body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: bold;
  margin: 0 0 10px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 10px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

/* Base */
.body-inner {
  padding-top: 50px;
  padding-bottom: 50px;
}

.wrapper {
  max-width: 700px;
}

.wrapper-inner {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 40px 50px 50px 50px;
}

.header {
  text-align: center;
  padding: 0 0 50px 0;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  padding-top: 10px;
  font-size: 14px;
  line-height: 24px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 24px;
}

/* Tables */
.content .field-name {
  padding-top: 10px;
  padding-bottom: 10px;
  /* Repeater & Layout */
}

.content .field-name:not(.field-value) {
  font-size: 18px;
  line-height: 20.7px;
}

.content .field-name.field-repeater-name, .content .field-name.field-layout-name {
  font-size: 22px;
  padding-top: 30px;
  padding-bottom: 30px;
}

.content .field-value {
  padding-bottom: 30px;
}

.content .field-name.field-value {
  line-height: 24px;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.container-content {
  padding: 80px;
}

.header {
  height: 160px;
  padding: 0;
  margin: 0;
}

.header[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

.header[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

.header[data-size="large"] .logo {
  height: 170px;
  max-height: 170px;
  max-width: 66%;
}

.header[data-size="full"] .logo {
  max-height: 170px;
  max-width: 100%;
}

h1.heading_1 {
  font-size: 64px;
  line-height: 50px;
  font-weight: 700;
  text-align: center;
  text-transform: uppercase;
  margin: 0 0 20px 0;
  padding: 0;
  color: #44403C;
}

h2.heading_2 {
  font-size: 36px;
  line-height: 28px;
  letter-spacing: 3.6px;
  font-weight: bold;
  text-align: center;
  text-transform: uppercase;
  margin: 0;
  padding: 0;
  color: #78716C;
}

h3.heading_3 {
  font-size: 16px;
  line-height: 13px;
  letter-spacing: 1.6px;
  font-weight: bold;
  text-align: center;
  text-transform: uppercase;
  margin: 0;
  padding: 0;
  color: #A8A29F;
}

.cname {
  font-size: 64px;
  line-height: 55px;
  font-weight: 700;
  text-align: center;
  margin: 10px 0 0 0;
  padding: 0;
  color: #FA7315;
}

.content {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  text-align: center;
  margin: 40px 0 0 0;
  padding: 0;
  color: #44403C;
}

.content p {
  line-height: 24px;
}

.divider {
  width: auto;
  margin: 50px;
}

table.divider {
  background: none;
  width: 82%;
  margin: 50px auto 50px auto;
  opacity: 1;
}

table.divider td {
  border-top: 1px solid #A8A29F;
  height: 1px;
  padding: 0;
}

table.divider td.divider-icon {
  border-top: none;
  width: 34px;
}

table.divider td.divider-icon img {
  margin: -8px 0 0 8px;
  width: 20px;
}

.footer {
  text-align: center;
  padding: 0;
}

.footer table {
  margin: 0 auto;
}

.footer table td {
  vertical-align: middle;
}

.footer .badge {
  position: relative;
  margin: 0 40px 0 10px;
  background-position: 0 0;
  background-size: 121px 121px;
  background-repeat: no-repeat;
  width: 121px;
  height: 121px;
  text-align: right;
}

.footer .badge img {
  width: 121px;
  height: 121px;
}

.footer .badge .badge_year,
.footer .badge .badge_subheading {
  position: absolute;
  font-size: 20px;
  line-height: 15px;
  top: 47px;
  left: 0;
  width: 100%;
  text-align: center;
  text-transform: uppercase;
}

.footer .badge .badge_year {
  font-weight: bold;
}

.footer .badge .badge_subheading {
  font-size: 12px;
  line-height: 12px;
  top: 67px;
  font-weight: bold;
}

.footer .signature {
  width: 200px;
  padding: 0;
  text-align: center;
}

.footer .signature .divider {
  width: auto;
  margin: 10px 0 10px 0;
}

.footer .signature .signature_text {
  font-size: 40px;
  line-height: 45px;
  font-weight: normal;
  margin: 0;
  padding: 0;
}

.footer .signature .signature_image {
  margin: 0 auto;
  padding: 0;
}

.footer .signature .signature_image[data-size="small"] {
  margin-top: 0;
}

.footer .signature .signature_image[data-size="medium"] {
  margin-top: -25px;
}

.footer .signature .signature_image[data-size="large"] {
  margin-top: -50px;
}

.footer .signature .signature_image[data-size="full"] {
  margin-top: -60px;
}

.footer .signature .signature_subheading {
  font-size: 14px;
  line-height: 13px;
  font-weight: normal;
  margin: 0;
  padding: 0;
}

.badge_year,
.badge_subheading {
  font-family: "Inter", sans-serif;
  color: #FA7315;
}

.signature_text {
  font-family: "Lobster", cursive;
  color: #78716C;
}

.signature_subheading {
  color: #A8A29F;
}

.landscape .container-content {
  padding: 40px;
}

.landscape .container-content .header {
  height: 150px;
}

.landscape .container-content .heading-container {
  text-align: center;
  white-space: nowrap;
}

.landscape .container-content .heading-container .heading_1,
.landscape .container-content .heading-container .heading_2 {
  display: inline-block;
  font-size: 54px;
  line-height: 54px;
  font-weight: 700;
  min-height: 54px;
  vertical-align: bottom;
  margin: 0 5px 0 5px;
}

.landscape .container-content .heading-container .heading_1:empty,
.landscape .container-content .heading-container .heading_2:empty {
  display: none;
}

.landscape .container-content .divider {
  margin-top: 40px;
  margin-bottom: 40px;
}

.landscape .container-content .divider.bottom {
  margin-top: 40px;
  margin-bottom: 0;
  visibility: hidden;
}

.landscape .container-content .content {
  margin: 30px 50px;
}

.landscape .container-content .footer .divider {
  margin: 10px 0 10px 0;
}

h1.heading_1 {
  font-family: "Literata", serif;
}

h2.heading_2 {
  font-family: "Literata", serif;
}

h3.heading_3 {
  font-family: "Literata", serif;
}

.cname {
  font-family: "Literata", serif;
}

.content {
  max-height: 100px !important;
  overflow: hidden;
  font-family: "Literata", serif !important;
}

.content p, .content b, .content strong, .content div, .content span, .content a {
  font-family: "Literata", serif !important;
}

.signature_subheading {
  font-family: "Literata", serif;
}

body.landscape .container-content .header {
  height: 160px;
}

body.landscape .container-content .header .logo {
  margin-top: 18px;
}

body.landscape .container-content .heading-container .heading_1,
body.landscape .container-content .heading-container .heading_2 {
  letter-spacing: -1px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
