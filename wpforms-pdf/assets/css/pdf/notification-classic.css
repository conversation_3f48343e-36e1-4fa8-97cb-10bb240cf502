body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: bold;
  margin: 0 0 7px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 14px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 7px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 7px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

/* Base */
.body-inner {
  padding-top: 50px;
  padding-bottom: 50px;
}

.wrapper {
  max-width: 660px;
}

.wrapper-inner {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  padding: 25px 30px 50px 30px;
}

.header {
  text-align: center;
  padding: 0 0 50px 0;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  font-size: 12px;
  line-height: 24px;
  text-align: center;
  padding-top: 25px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 22px;
}

/* Content */
.content .field-name {
  padding-top: 25px;
  padding-bottom: 7px;
}

.content .field-value {
  padding-bottom: 25px;
  border-bottom: 1px solid #d9d9d9;
}

.content .field-name.field-value {
  line-height: 22px;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview {
  border-radius: 0;
}

.wpforms-layout-table > td {
  border-bottom: 1px solid #d9d9d9;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row td.field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row td.field-value {
  padding-bottom: 0;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.container-background-wrapper {
  border-radius: 0;
}

.container-background-wrapper > .header {
  visibility: hidden !important;
}

.container-background-wrapper .container-background-fill {
  top: 0;
  right: 0;
  left: 0;
  bottom: unset;
  border-radius: 0;
  padding: 30px;
}

.container-background-wrapper .container-background-fill > .header,
.container-background-wrapper .container-background-fill > .content,
.container-background-wrapper .container-background-fill > .footer {
  visibility: hidden !important;
}

.container-background-wrapper .container-shadow {
  bottom: unset;
}

.content table tbody tr:first-child td {
  padding-top: 0;
}

.divider-invisible {
  visibility: hidden;
  height: 0;
  max-height: 0;
}

body {
  margin-top: 45px;
}

.header {
  margin: 0 -30px 30px -30px;
  padding: 0 30px;
}

.header[data-logo=""]:has(img[src=""]) {
  margin-bottom: 0;
}

.header:has(img[src=""]) {
  margin-bottom: 0;
}

.container-content .content {
  margin-top: 30px;
}

.header {
  margin: 0 -30px 30px -30px;
}

.container-content {
  margin: -30px 45px;
  padding: 30px 30px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicGRmL25vdGlmaWNhdGlvbi1jbGFzc2ljLmNzcyIsInNvdXJjZXMiOlsicGRmL25vdGlmaWNhdGlvbi1jbGFzc2ljLnNjc3MiLCIuLi8uLi8uLi93cGZvcm1zL2Fzc2V0cy9zY3NzL2VtYWlscy9jbGFzc2ljLnNjc3MiLCIuLi8uLi8uLi93cGZvcm1zL2Fzc2V0cy9zY3NzL2VtYWlscy9wYXJ0aWFscy9fbm90aWZpY2F0aW9ucy5zY3NzIiwiLi4vLi4vLi4vd3Bmb3Jtcy9hc3NldHMvc2Nzcy9lbWFpbHMvcGFydGlhbHMvX3Jlc2V0cy5zY3NzIiwiLi4vLi4vLi4vd3Bmb3Jtcy9hc3NldHMvc2Nzcy9lbWFpbHMvcGFydGlhbHMvX3RleHQuc2NzcyIsIi4uLy4uLy4uL3dwZm9ybXMvYXNzZXRzL3Njc3MvcGFydGlhbHMvZmllbGRzL3BheW1lbnRzL19vcmRlci1zdW1tYXJ5LnNjc3MiLCIuLi8uLi8uLi93cGZvcm1zL2Fzc2V0cy9zY3NzL2VtYWlscy9wYXJ0aWFscy9fY2xhc3NpYy5zY3NzIiwiLi4vLi4vLi4vd3Bmb3Jtcy9hc3NldHMvc2Nzcy9lbWFpbHMvcGFydGlhbHMvX2xheW91dC5zY3NzIiwicGRmL3BhcnRpYWxzL19jb21tb24uc2NzcyIsInBkZi9wYXJ0aWFscy9fdmFycy5zY3NzIiwicGRmL3BhcnRpYWxzL19mb250cy5zY3NzIiwicGRmL3BhcnRpYWxzL19ub3RpZmljYXRpb24uc2NzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBXUEZvcm1zIFBERiBzdHlsZXMuXG4vL1xuLy8gVGVtcGxhdGU6IG5vdGlmaWNhdGlvbi1jbGFzc2ljXG4vL1xuLy8gQHNpbmNlIDEuMC4wXG5cbi8vIEltcG9ydCBjbGFzc2ljIGVtYWlsIHN0eWxlIGZyb20gdGhlIGNvcmUgcGx1Z2luLlxuQGltcG9ydCAnLi4vLi4vLi4vLi4vd3Bmb3Jtcy9hc3NldHMvc2Nzcy9lbWFpbHMvY2xhc3NpYyc7XG5cbi8vIENvbW1vbiBwYXJ0IGZvciBhbGwgdGhlIHRlbXBsYXRlcy5cbkBpbXBvcnQgXCJwYXJ0aWFscy9jb21tb25cIjtcblxuJGJvZHlfbWFyZ2luOiA0NXB4O1xuJGNvbnRhaW5lcl9wYWRkaW5nX3ZlcnRpY2FsOiAzMHB4O1xuJGNvbnRhaW5lcl9wYWRkaW5nX2hvcml6b250YWw6IDMwcHg7XG4kY29udGFpbmVyX3BhZGRpbmc6ICRjb250YWluZXJfcGFkZGluZ192ZXJ0aWNhbDtcbiRib3JkZXJfcmFkaXVzOiAwO1xuXG4vLyBDb21tb24gcGFydCBmb3IgYWxsIHRoZSBub3RpZmljYXRpb24gdGVtcGxhdGVzLlxuQGltcG9ydCBcInBhcnRpYWxzL25vdGlmaWNhdGlvblwiO1xuXG4uaGVhZGVyIHtcblx0bWFyZ2luOiAwIC0jeyRjb250YWluZXJfcGFkZGluZ19ob3Jpem9udGFsfSAjeyRjb250YWluZXJfcGFkZGluZ30gLSN7JGNvbnRhaW5lcl9wYWRkaW5nX2hvcml6b250YWx9O1xufVxuXG4uY29udGFpbmVyLWNvbnRlbnQge1xuXHRtYXJnaW46IC0kY29udGFpbmVyX3BhZGRpbmdfdmVydGljYWwgJGJvZHlfbWFyZ2luO1xuXHRwYWRkaW5nOiAkY29udGFpbmVyX3BhZGRpbmdfdmVydGljYWwgJGNvbnRhaW5lcl9wYWRkaW5nX2hvcml6b250YWw7XG59XG4iLCIkbWFyZ2luQm90dG9tOiA3cHg7XG4kZm9udFNpemU6IDE0cHg7XG4kZm9udEZhbWlseTogLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCBhdmVuaXIgbmV4dCwgYXZlbmlyLCBzZWdvZSB1aSwgaGVsdmV0aWNhIG5ldWUsIGhlbHZldGljYSwgQ2FudGFyZWxsLCBVYnVudHUsIHJvYm90bywgbm90bywgYXJpYWwsIHNhbnMtc2VyaWY7XG5cbkBpbXBvcnQgJ3BhcnRpYWxzL25vdGlmaWNhdGlvbnMnO1xuQGltcG9ydCAncGFydGlhbHMvY2xhc3NpYyc7XG5AaW1wb3J0ICdwYXJ0aWFscy9sYXlvdXQnO1xuIiwiJHByaW1hcnk6ICMzMzMzMzM7XG4kb3JhbmdlOiAjZTI3NzMwO1xuJGJsdWU6ICM1MDlmZTI7XG4kZ3JlZW46ICM3NGFlNWU7XG4kbGlua0NvbG9yOiAjZTU3NzIyO1xuJGxpbmtDb2xvckhvdmVyOiBkYXJrZW4oICRsaW5rQ29sb3IsIDIwJSApO1xuJGJhY2tncm91bmRDb2xvcjogI2U5ZWFlYztcbiRiYWNrZ3JvdW5kQ29udGVudDogI2ZmZmZmZjtcbiRmb250Q29sb3I6ICMzMzMzMzM7XG4kZXJyb3I6ICNkNjM2Mzg7XG5cbkBpbXBvcnQgJ3Jlc2V0cyc7XG5AaW1wb3J0ICd0ZXh0JztcblxuLyogQmFzZSAqL1xuLmJvZHksXG5ib2R5IHtcblx0YmFja2dyb3VuZC1jb2xvcjogJGJhY2tncm91bmRDb2xvcjtcblx0dGV4dC1hbGlnbjogY2VudGVyO1xuXHRwYWRkaW5nOiAwIDI1cHggMCAyNXB4O1xufVxuXG4uY29udGFpbmVyIHtcblx0bWFyZ2luOiAwIGF1dG8gMCBhdXRvO1xufVxuXG4uaGVhZGVyIHtcblx0bGluZS1oZWlnaHQ6IDE7XG5cblx0LmhlYWRlci1pbWFnZSB7XG5cdFx0ZGlzcGxheTogaW5saW5lLWJsb2NrO1xuXHRcdHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XG5cdFx0d2lkdGg6IDgwJTtcblx0fVxuXG5cdGltZyB7XG5cdFx0ZGlzcGxheTogaW5saW5lLWJsb2NrICFpbXBvcnRhbnQ7XG5cdFx0bWF4LWhlaWdodDogMTgwcHg7IC8vIERlZmF1bHQgXCJtZWRpdW1cIiBoZWFkZXIgaW1hZ2UgaGVpZ2h0LlxuXHRcdHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XG5cdH1cbn1cblxuLy8gSGlkZSB0aGUgZGFyayB2YXJpYXRpb24gYnkgZGVmYXVsdC5cbi5oZWFkZXItd3JhcHBlciB7XG5cdCYuZGFyay1tb2RlIHtcblx0XHRkaXNwbGF5OiBub25lO1xuXHR9XG59XG5cbi5jb250ZW50IHtcblxuXHRhLCBwLCBwcmUge1xuXHRcdC1tcy13b3JkLWJyZWFrOiBicmVhay13b3JkO1xuXHRcdHdvcmQtYnJlYWs6IGJyZWFrLXdvcmQ7XG5cdH1cblxuXHRwcmUge1xuXHRcdHdoaXRlLXNwYWNlOiBpbml0aWFsO1xuXHR9XG5cblx0LyogSGVscGVyIGNsYXNzIGZvciBpbmxpbmUgZWxlbWVudHMuICovXG5cdC5pbmxpbmUge1xuXHRcdGRpc3BsYXk6IGlubGluZS1ibG9jaztcblx0fVxuXG5cdC5zbWFydC10YWcge1xuXHRcdHRhYmxlOm5vdCgud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LXByZXZpZXcpIHtcblx0XHRcdGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7XG5cdFx0XHR3aWR0aDogMTAwJTtcblxuXHRcdFx0dGQsIHRoIHtcblx0XHRcdFx0Ym9yZGVyOiAxcHggc29saWQgY3VycmVudENvbG9yO1xuXHRcdFx0XHRwYWRkaW5nOiA1cHggIWltcG9ydGFudDtcblx0XHRcdH1cblx0XHR9XG5cdH1cbn1cblxuLmNvbnRlbnQgdGQgPiAqOmxhc3QtY2hpbGQge1xuXHRtYXJnaW4tYm90dG9tOiAwO1xufVxuXG4uZm9vdGVyIHtcblx0Y29sb3I6IGxpZ2h0ZW4oJGZvbnRDb2xvciwgNDAlKTtcblxuXHRhIHtcblx0XHRjb2xvcjogbGlnaHRlbigkZm9udENvbG9yLCA0MCUpO1xuXHRcdHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuXG5cdFx0Jjpob3ZlciB7XG5cdFx0XHRjb2xvcjogJGZvbnRDb2xvcjtcblx0XHR9XG5cdH1cbn1cblxuLyogVHlwb2dyYXBoeSAqL1xuaDEsXG5oMixcbmgzLFxuaDQsXG5oNSxcbmg2IHtcblx0Y29sb3I6ICRmb250Q29sb3I7XG59XG5cbi8qIEJ1dHRvbnMgKi9cbi5idXR0b24tbGluayB7XG5cdGJvcmRlci1yYWRpdXM6IDNweDtcblx0cGFkZGluZzogN3B4IDE1cHg7XG5cdHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn1cblxuLyogQ29udGVudCAqL1xuJGZpZWxkczogc2lnbmF0dXJlLCByYXRpbmc7XG5cbkBlYWNoICRmaWVsZCBpbiAkZmllbGRzIHtcblx0LmZpZWxkLSN7JGZpZWxkfSB7XG5cdFx0dGQuZmllbGQtdmFsdWUge1xuXHRcdFx0bGluZS1oZWlnaHQ6IDE7XG5cdFx0fVxuXHR9XG59XG5cbnRyOm5vdCguc21hcnQtdGFnKSB7XG5cdD4gLmZpZWxkLXZhbHVlIHNwYW4ge1xuXHRcdGRpc3BsYXk6IGJsb2NrO1xuXHR9XG59XG5cbi8qIFJlcGVhdGVyICYgTGF5b3V0ICovXG4uZmllbGQtcmVwZWF0ZXItbmFtZSxcbi5maWVsZC1sYXlvdXQtbmFtZSB7XG5cdGZvbnQtc2l6ZTogMjJweDtcbn1cblxuLyogRmlsZSBVcGxvYWQgKi9cbi5maWVsZC1maWxlLXVwbG9hZCB7XG5cdC5maWVsZC12YWx1ZSAuZmlsZS1pY29uIHtcblx0XHRkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG5cdFx0dmVydGljYWwtYWxpZ246IG1pZGRsZTtcblx0fVxufVxuXG4vKiBSaWNoVGV4dCwgQ29udGVudCAqL1xuLmZpZWxkLXJpY2h0ZXh0LCAuZmllbGQtY29udGVudCB7XG5cdC5maWVsZC12YWx1ZTpvbmx5LWNoaWxkIHtcblx0XHRkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG5cdFx0d2lkdGg6IDEwMCU7XG5cdH1cblxuXHRwIC5hbGlnbmxlZnQsXG5cdGxpIC5hbGlnbmxlZnQge1xuXHRcdGZsb2F0OiBsZWZ0O1xuXHRcdG1hcmdpbi1yaWdodDogMTZweDtcblx0XHRtYXJnaW4tdG9wOiA4cHg7XG5cdFx0bWFyZ2luLWJvdHRvbTogOHB4O1xuXHR9XG5cblx0cCAuYWxpZ25jZW50ZXIsXG5cdGxpIC5hbGlnbmNlbnRlciB7XG5cdFx0ZGlzcGxheTogYmxvY2s7XG5cdFx0bWFyZ2luLWxlZnQ6IGF1dG87XG5cdFx0bWFyZ2luLXJpZ2h0OiBhdXRvO1xuXHR9XG5cblx0cCAuYWxpZ25yaWdodCxcblx0bGkgLmFsaWducmlnaHQge1xuXHRcdGZsb2F0OiByaWdodDtcblx0XHRtYXJnaW4tbGVmdDogMTZweDtcblx0XHRtYXJnaW4tdG9wOiA4cHg7XG5cdFx0bWFyZ2luLWJvdHRvbTogOHB4O1xuXHR9XG5cblx0dGFibGUge1xuXHRcdGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7XG5cdFx0d2lkdGg6IDEwMCU7XG5cblx0XHR0ZCwgdGgge1xuXHRcdFx0Ym9yZGVyOiAxcHggc29saWQgY3VycmVudENvbG9yO1xuXHRcdFx0cGFkZGluZzogNXB4ICFpbXBvcnRhbnQ7XG5cdFx0fVxuXHR9XG59XG5cbi5maWVsZC1yYXRpbmcge1xuXHQuZmllbGQtdmFsdWUge1xuXHRcdGxpbmUtaGVpZ2h0OiAxLjMgIWltcG9ydGFudDtcblx0fVxufVxuXG4vLyBPcmRlciBzdW1tYXJ5IHRhYmxlLlxuQGltcG9ydCAnLi4vLi4vcGFydGlhbHMvZmllbGRzL3BheW1lbnRzL29yZGVyLXN1bW1hcnknO1xuLmZpZWxkLXBheW1lbnQtdG90YWwsXG4uc21hcnQtdGFnIHtcblx0QGluY2x1ZGUgb3JkZXItc3VtbWFyeS1jb21tb247XG5cdEBpbmNsdWRlIG9yZGVyLXN1bW1hcnktZmFuY3k7XG5cblx0LndwZm9ybXMtb3JkZXItc3VtbWFyeS1jb250YWluZXIge1xuXHRcdG1heC13aWR0aDogMTAwJTtcblxuXHRcdHRhYmxlLndwZm9ybXMtb3JkZXItc3VtbWFyeS1wcmV2aWV3IHtcblx0XHRcdGNhcHRpb24sXG5cdFx0XHQud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LXBsYWNlaG9sZGVyLWhpZGRlbixcblx0XHRcdC53cGZvcm1zLW9yZGVyLXN1bW1hcnktaXRlbS1xdWFudGl0eS1sYWJlbC1zaG9ydCB7XG5cdFx0XHRcdGRpc3BsYXk6IG5vbmU7XG5cdFx0XHR9XG5cblx0XHRcdHRyIHtcblx0XHRcdFx0Ji53cGZvcm1zLW9yZGVyLXN1bW1hcnktcHJldmlldy1jb3Vwb24tdG90YWwgdGQud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LWl0ZW0tcHJpY2Uge1xuXHRcdFx0XHRcdGNvbG9yOiAkZXJyb3IgIWltcG9ydGFudDtcblx0XHRcdFx0fVxuXHRcdFx0fVxuXHRcdH1cblx0fVxufVxuIiwiYm9keSxcbi5ib2R5IHtcblx0aGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7XG5cdG1hcmdpbjogMDtcblx0cGFkZGluZzogMDtcblx0d2lkdGg6IDEwMCUgIWltcG9ydGFudDtcblx0bWluLXdpZHRoOiAxMDAlO1xuXHQtbW96LWJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDtcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0LXdlYmtpdC1mb250LXNtb290aGluZzogYW50aWFsaWFzZWQgIWltcG9ydGFudDtcblx0LW1vei1vc3gtZm9udC1zbW9vdGhpbmc6IGdyYXlzY2FsZSAhaW1wb3J0YW50O1xufVxuXG5pbWcge1xuXHRvdXRsaW5lOiBub25lO1xuXHR0ZXh0LWRlY29yYXRpb246IG5vbmU7XG5cdC1tcy1pbnRlcnBvbGF0aW9uLW1vZGU6IGJpY3ViaWM7XG5cdHdpZHRoOiBhdXRvO1xuXHRtYXgtd2lkdGg6IDEwMCU7XG5cdGNsZWFyOiBib3RoO1xuXHRkaXNwbGF5OiBibG9jaztcbn1cblxuYSBpbWcge1xuXHRib3JkZXI6IG5vbmU7XG59XG5cbnAge1xuXHRtYXJnaW46IDAgMCAxMHB4IDA7XG59XG5cbnRhYmxlIHtcblx0Ym9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTtcblx0Ym9yZGVyLXNwYWNpbmc6IDA7XG59XG5cbnRkIHtcblx0d29yZC13cmFwOiBicmVhay13b3JkO1xuXHQtd2Via2l0LWh5cGhlbnM6IGF1dG87XG5cdC1tb3otaHlwaGVuczogYXV0bztcblx0aHlwaGVuczogYXV0bztcblx0Ym9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZSAhaW1wb3J0YW50O1xufVxuXG50YWJsZSxcbnRyLFxudGQge1xuXHRwYWRkaW5nOiAwO1xuXHR2ZXJ0aWNhbC1hbGlnbjogdG9wO1xufVxuXG5oMSxcbmgyLFxuaDMsXG5oNCxcbmg1LFxuaDYge1xuXHRtYXJnaW46IDA7XG5cdHBhZGRpbmc6IDA7XG59XG5cbi5SZWFkTXNnQm9keSxcbi5FeHRlcm5hbENsYXNzIHtcblx0d2lkdGg6IDEwMCU7XG59XG5cbi5FeHRlcm5hbENsYXNzIHtcblx0d2lkdGg6IDEwMCU7XG59XG5cbi5FeHRlcm5hbENsYXNzLFxuLkV4dGVybmFsQ2xhc3MgcCxcbi5FeHRlcm5hbENsYXNzIHNwYW4sXG4uRXh0ZXJuYWxDbGFzcyBmb250LFxuLkV4dGVybmFsQ2xhc3MgdGQsXG4uRXh0ZXJuYWxDbGFzcyBkaXYge1xuXHRsaW5lLWhlaWdodDogMTAwJTtcbn1cblxudGFibGUsXG50ZCB7XG5cdG1zby10YWJsZS1sc3BhY2U6IDBwdDtcblx0bXNvLXRhYmxlLXJzcGFjZTogMHB0O1xufVxuXG4jb3V0bG9vayBhIHtcblx0cGFkZGluZzogMDtcbn1cblxuaW1nIHtcblx0LW1zLWludGVycG9sYXRpb24tbW9kZTogYmljdWJpYztcbn1cblxuYm9keSwgdGFibGUsIHRkLCBwLCBhLCBsaSwgYmxvY2txdW90ZSB7XG5cdC1tcy10ZXh0LXNpemUtYWRqdXN0OiAxMDAlO1xuXHQtd2Via2l0LXRleHQtc2l6ZS1hZGp1c3Q6IDEwMCU7XG59XG4iLCJib2R5LFxudGFibGUuYm9keSxcbmgxLFxuaDIsXG5oMyxcbmg0LFxuaDUsXG5oNixcbnAsXG50ZCxcbnRoLFxuYSB7XG5cdGNvbG9yOiAkZm9udENvbG9yO1xuXHRmb250LWZhbWlseTogJGZvbnRGYW1pbHk7XG5cdGZvbnQtd2VpZ2h0OiBub3JtYWw7XG5cdHBhZGRpbmc6IDA7XG5cdG1hcmdpbjogMDtcblx0bXNvLWxpbmUtaGVpZ2h0LXJ1bGU6IGV4YWN0bHk7XG5cdGxpbmUtaGVpZ2h0OiAxLjQ7XG5cdGxpbmUtaGVpZ2h0OiAxNDAlO1xufVxuXG5oMSxcbmgyLFxuaDMsXG5oNCxcbmg1LFxuaDYge1xuXHRjb2xvcjogIzQ0NDQ0NDtcblx0d29yZC13cmFwOiBub3JtYWw7XG5cdGZvbnQtZmFtaWx5OiAkZm9udEZhbWlseTtcblx0Zm9udC13ZWlnaHQ6IGJvbGQ7XG5cdG1hcmdpbjogMCAwICRtYXJnaW5Cb3R0b20gMDtcblx0bXNvLWxpbmUtaGVpZ2h0LXJ1bGU6IGV4YWN0bHk7XG5cdGxpbmUtaGVpZ2h0OiAxLjM7XG5cdGxpbmUtaGVpZ2h0OiAxMzAlO1xuXG5cdCYubm9ybWFsIHtcblx0XHRmb250LXdlaWdodDogbm9ybWFsO1xuXHR9XG59XG5cbmgxIHtcblx0Zm9udC1zaXplOiAzMnB4O1xufVxuXG5oMiB7XG5cdGZvbnQtc2l6ZTogMzBweDtcbn1cblxuaDMge1xuXHRmb250LXNpemU6IDI4cHg7XG59XG5cbmg0IHtcblx0Zm9udC1zaXplOiAyNHB4O1xufVxuXG5oNSB7XG5cdGZvbnQtc2l6ZTogMjBweDtcbn1cblxuaDYge1xuXHRmb250LXNpemU6IDE4cHg7XG59XG5cbmJvZHksXG50YWJsZS5ib2R5LFxucCxcbnRkLFxudGgge1xuXHRmb250LXNpemU6ICRmb250U2l6ZTtcblx0bXNvLWxpbmUtaGVpZ2h0LXJ1bGU6IGV4YWN0bHk7XG5cdGxpbmUtaGVpZ2h0OiAxLjQ7XG5cdGxpbmUtaGVpZ2h0OiAxNDAlO1xufVxuXG5wIHtcblx0bWFyZ2luOiAwIDAgJG1hcmdpbkJvdHRvbSAwO1xuXG5cdG92ZXJmbG93LXdyYXA6IGJyZWFrLXdvcmQ7XG5cdHdvcmQtd3JhcDogYnJlYWstd29yZDtcblxuXHQtbXMtd29yZC1icmVhazogYnJlYWstYWxsO1xuXHR3b3JkLWJyZWFrOiBicmVhay1hbGw7XG5cblx0LW1zLWh5cGhlbnM6IGF1dG87XG5cdC1tb3otaHlwaGVuczogYXV0bztcblx0LXdlYmtpdC1oeXBoZW5zOiBhdXRvO1xuXHRoeXBoZW5zOiBhdXRvO1xuXG5cdCYubGFyZ2UsXG5cdCYudGV4dC1sYXJnZSB7XG5cdFx0Zm9udC1zaXplOiAxNnB4O1xuXHR9XG5cblx0Ji5ib2xkLFxuXHQmLnRleHQtYm9sZCB7XG5cdFx0Zm9udC13ZWlnaHQ6IDcwMDtcblx0fVxuXG5cdGEge1xuXHRcdG1hcmdpbjogaW5oZXJpdDtcblx0fVxufVxuXG5zbWFsbCB7XG5cdGZvbnQtc2l6ZTogODAlO1xufVxuXG5jZW50ZXIge1xuXHR3aWR0aDogMTAwJTtcbn1cblxuYSB7XG5cdGNvbG9yOiAkbGlua0NvbG9yO1xuXG5cdCY6dmlzaXRlZCB7XG5cdFx0Y29sb3I6ICRsaW5rQ29sb3I7XG5cdH1cblxuXHQmOmhvdmVyLFxuXHQmOmFjdGl2ZSB7XG5cdFx0Y29sb3I6ICRsaW5rQ29sb3JIb3Zlcjtcblx0fVxufVxuXG5oMSBhLFxuaDEgYTp2aXNpdGVkLFxuaDIgYSxcbmgyIGE6dmlzaXRlZCxcbmgzIGEsXG5oMyBhOnZpc2l0ZWQsXG5oNCBhLFxuaDQgYTp2aXNpdGVkLFxuaDUgYSxcbmg1IGE6dmlzaXRlZCxcbmg2IGEsXG5oNiBhOnZpc2l0ZWQge1xuXHRjb2xvcjogJGxpbmtDb2xvcjtcbn1cblxudGFibGUudGV4dC1jZW50ZXIsXG50aC50ZXh0LWNlbnRlcixcbnRkLnRleHQtY2VudGVyLFxuaDEudGV4dC1jZW50ZXIsXG5oMi50ZXh0LWNlbnRlcixcbmgzLnRleHQtY2VudGVyLFxuaDQudGV4dC1jZW50ZXIsXG5oNS50ZXh0LWNlbnRlcixcbmg2LnRleHQtY2VudGVyLFxucC50ZXh0LWNlbnRlcixcbnNwYW4udGV4dC1jZW50ZXIge1xuXHR0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbnRhYmxlLnRleHQtbGVmdCxcbnRoLnRleHQtbGVmdCxcbnRkLnRleHQtbGVmdCxcbmgxLnRleHQtbGVmdCxcbmgyLnRleHQtbGVmdCxcbmgzLnRleHQtbGVmdCxcbmg0LnRleHQtbGVmdCxcbmg1LnRleHQtbGVmdCxcbmg2LnRleHQtbGVmdCxcbnAudGV4dC1sZWZ0LFxuc3Bhbi50ZXh0LWxlZnQge1xuXHR0ZXh0LWFsaWduOiBsZWZ0O1xufVxuXG50YWJsZS50ZXh0LXJpZ2h0LFxudGgudGV4dC1yaWdodCxcbnRkLnRleHQtcmlnaHQsXG5oMS50ZXh0LXJpZ2h0LFxuaDIudGV4dC1yaWdodCxcbmgzLnRleHQtcmlnaHQsXG5oNC50ZXh0LXJpZ2h0LFxuaDUudGV4dC1yaWdodCxcbmg2LnRleHQtcmlnaHQsXG5wLnRleHQtcmlnaHQsXG5zcGFuLnRleHQtcmlnaHQge1xuXHR0ZXh0LWFsaWduOiByaWdodDtcbn1cblxudGFibGUucHJpbWFyeSxcbnRoLnByaW1hcnksXG50ZC5wcmltYXJ5LFxuaDEucHJpbWFyeSxcbmgyLnByaW1hcnksXG5oMy5wcmltYXJ5LFxuaDQucHJpbWFyeSxcbmg1LnByaW1hcnksXG5oNi5wcmltYXJ5LFxucC5wcmltYXJ5LFxuc3Bhbi5wcmltYXJ5IHtcblx0Y29sb3I6ICRwcmltYXJ5O1xufVxuXG50YWJsZS5vcmFuZ2UsXG50aC5vcmFuZ2UsXG50ZC5vcmFuZ2UsXG5oMS5vcmFuZ2UsXG5oMi5vcmFuZ2UsXG5oMy5vcmFuZ2UsXG5oNC5vcmFuZ2UsXG5oNS5vcmFuZ2UsXG5oNi5vcmFuZ2UsXG5wLm9yYW5nZSxcbnNwYW4ub3JhbmdlIHtcblx0Y29sb3I6ICRvcmFuZ2U7XG59XG5cbnRhYmxlLmJsdWUsXG50aC5ibHVlLFxudGQuYmx1ZSxcbmgxLmJsdWUsXG5oMi5ibHVlLFxuaDMuYmx1ZSxcbmg0LmJsdWUsXG5oNS5ibHVlLFxuaDYuYmx1ZSxcbnAuYmx1ZSxcbnNwYW4uYmx1ZSB7XG5cdGNvbG9yOiAkYmx1ZTtcbn1cblxuc3Bhbi50ZXh0LWNlbnRlciB7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMTAwJTtcblx0dGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG5vbCxcbnVsIHtcblx0bWFyZ2luOiAwIDAgJG1hcmdpbkJvdHRvbSAyMHB4O1xuXHRwYWRkaW5nOiAwO1xuXG5cdGxpIHtcblx0XHRsaXN0LXN0eWxlLXR5cGU6IGRlY2ltYWw7XG5cdFx0cGFkZGluZy10b3A6IDVweDtcblx0fVxuXG5cdG9sLFxuXHR1bCB7XG5cdFx0bWFyZ2luLWJvdHRvbTogMCAhaW1wb3J0YW50O1xuXHR9XG59XG5cbi8qIEhlbHBlciBjbGFzcyBmb3IgYnJlYWtpbmcgbG9uZyBVUkxzLiAqL1xuLmJyZWFrLWFsbCB7XG5cdHdvcmQtYnJlYWs6IGJyZWFrLWFsbCAhaW1wb3J0YW50O1xuXG5cdD4gYSB7XG5cdFx0d29yZC1icmVhazogYnJlYWstYWxsICFpbXBvcnRhbnQ7XG5cdH1cbn1cbiIsIi8vIFBheW1lbnQgdG90YWwgZmllbGQ6IE9yZGVyIFN1bW1hcnkgdmlldy5cbi8vXG4vLyBUaGVzZSBtaXhpbnMgYXJlIHVzZWQgb246XG4vLyAtIGZvcm0gcHJldmlld1xuLy8gLSBidWlsZGVyIHNjcmVlblxuLy8gLSBlbWFpbCBub3RpZmljYXRpb25zXG4vL1xuLy8gQHNpbmNlIDEuOC43XG5cbiRmaWVsZF9zaXplX21lZGl1bTogNjAlO1xuJGZpZWxkX3NpemVfbGFyZ2U6IDEwMCU7XG5cbkBtaXhpbiBvcmRlci1zdW1tYXJ5LWNvbW1vbiB7XG5cdC53cGZvcm1zLW9yZGVyLXN1bW1hcnktY29udGFpbmVyIHtcblx0XHQqe1xuXHRcdFx0d29yZC1icmVhazogYnJlYWstd29yZDtcblx0XHRcdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdFx0fVxuXG5cdFx0ZGlzcGxheTogYmxvY2s7XG5cdFx0bWF4LXdpZHRoOiAkZmllbGRfc2l6ZV9tZWRpdW07XG5cblx0XHR0YWJsZS53cGZvcm1zLW9yZGVyLXN1bW1hcnktcHJldmlldyB7XG5cdFx0XHR3aWR0aDogMTAwJTtcblx0XHRcdHRhYmxlLWxheW91dDogZml4ZWQ7XG5cblx0XHRcdHRyIHtcblx0XHRcdFx0dGQsIHRoIHtcblx0XHRcdFx0XHR0ZXh0LWFsaWduOiBjZW50ZXI7XG5cblx0XHRcdFx0XHQvLyBQcm9kdWN0IE5hbWUgKEl0ZW0pIGNvbHVtbi5cblx0XHRcdFx0XHQmLndwZm9ybXMtb3JkZXItc3VtbWFyeS1pdGVtLWxhYmVsIHtcblx0XHRcdFx0XHRcdHRleHQtYWxpZ246IGxlZnQ7XG5cblx0XHRcdFx0XHRcdC5ydGwgJiB7XG5cdFx0XHRcdFx0XHRcdHRleHQtYWxpZ246IHJpZ2h0O1xuXHRcdFx0XHRcdFx0fVxuXHRcdFx0XHRcdH1cblxuXHRcdFx0XHRcdC8vIFF1YW50aXR5IGNvbHVtbi5cblx0XHRcdFx0XHQvL1xuXHRcdFx0XHRcdC8vIDggY2hhcnMgbGVuZ3RoIGlzIHRoZSBtYXggd2lkdGggZm9yIHRoaXMgY29sdW1uIGJlY2F1c2Ugb2Zcblx0XHRcdFx0XHQvLyBoZWFkaW5nOiBRdWFudGl0eSAgICAgICAgICAgLSA4IGNoYXJzXG5cdFx0XHRcdFx0Ly8gdmFsdWU6ICAgOTk5OSAobWF4IGFsbG93ZWQpIC0gNCBjaGFyc1xuXHRcdFx0XHRcdCYud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LWl0ZW0tcXVhbnRpdHkge1xuXHRcdFx0XHRcdFx0d2lkdGg6IDhjaDtcblxuXHRcdFx0XHRcdFx0LndwZm9ybXMtb3JkZXItc3VtbWFyeS1pdGVtLXF1YW50aXR5LWxhYmVsLXNob3J0IHtcblx0XHRcdFx0XHRcdFx0ZGlzcGxheTogbm9uZTtcblx0XHRcdFx0XHRcdH1cblxuXHRcdFx0XHRcdFx0QG1lZGlhIChtYXgtd2lkdGg6IDYwMHB4KSB7XG5cdFx0XHRcdFx0XHRcdC8vIDQgY2hhcnMgbGVuZ3RoIGlzIHRoZSBtYXggd2lkdGggZm9yIHRoaXMgY29sdW1uIGJlY2F1c2Ugb2Zcblx0XHRcdFx0XHRcdFx0Ly8gaGVhZGluZzogUXR5IC0gMyBjaGFyc1xuXHRcdFx0XHRcdFx0XHQvLyB2YWx1ZTogICA5OTk5IChtYXggYWxsb3dlZCkgLSA0IGNoYXJzXG5cdFx0XHRcdFx0XHRcdHdpZHRoOiA0Y2g7XG5cblx0XHRcdFx0XHRcdFx0LndwZm9ybXMtb3JkZXItc3VtbWFyeS1pdGVtLXF1YW50aXR5LWxhYmVsLXNob3J0IHtcblx0XHRcdFx0XHRcdFx0XHRkaXNwbGF5OiBpbmxpbmU7XG5cdFx0XHRcdFx0XHRcdH1cblxuXHRcdFx0XHRcdFx0XHQud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LWl0ZW0tcXVhbnRpdHktbGFiZWwtZnVsbCB7XG5cdFx0XHRcdFx0XHRcdFx0ZGlzcGxheTogbm9uZTtcblx0XHRcdFx0XHRcdFx0fVxuXHRcdFx0XHRcdFx0fVxuXHRcdFx0XHRcdH1cblxuXHRcdFx0XHRcdC8vIFByaWNlIGNvbHVtbi5cblx0XHRcdFx0XHQvLyBXaWR0aCBvZiB0aGlzIGNvbHVtbiBpcyBkZXRlcm1pbmVkIGR5bmFtaWNhbGx5XG5cdFx0XHRcdFx0Ly8gYW5kIGRlcGVuZHMgb24gaXRlbSBwcmljZXMuXG5cdFx0XHRcdFx0Ji53cGZvcm1zLW9yZGVyLXN1bW1hcnktaXRlbS1wcmljZSB7XG5cdFx0XHRcdFx0XHR3aWR0aDogNmNoO1xuXHRcdFx0XHRcdFx0dGV4dC1hbGlnbjogcmlnaHQ7XG5cblx0XHRcdFx0XHRcdC5ydGwgJiB7XG5cdFx0XHRcdFx0XHRcdHRleHQtYWxpZ246IGxlZnQ7XG5cdFx0XHRcdFx0XHR9XG5cdFx0XHRcdFx0fVxuXHRcdFx0XHR9XG5cblx0XHRcdFx0Ly8gUGxhY2Vob2xkZXIgcm93LlxuXHRcdFx0XHQmLndwZm9ybXMtb3JkZXItc3VtbWFyeS1wbGFjZWhvbGRlciB0ZCB7XG5cdFx0XHRcdFx0dGV4dC1hbGlnbjogbGVmdDtcblxuXHRcdFx0XHRcdC5ydGwgJiB7XG5cdFx0XHRcdFx0XHR0ZXh0LWFsaWduOiByaWdodDtcblx0XHRcdFx0XHR9XG5cdFx0XHRcdH1cblx0XHRcdH1cblx0XHR9XG5cdH1cbn1cblxuQG1peGluIG9yZGVyLXN1bW1hcnktc2l6ZXMge1xuXHQuc2l6ZS1sYXJnZSxcblx0LndwZm9ybXMtZmllbGQtbGFyZ2Uge1xuXHRcdD4gLndwZm9ybXMtb3JkZXItc3VtbWFyeS1jb250YWluZXIge1xuXHRcdFx0bWF4LXdpZHRoOiAkZmllbGRfc2l6ZV9sYXJnZTtcblx0XHR9XG5cdH1cblxuXHQuc2l6ZS1tZWRpdW0sXG5cdC53cGZvcm1zLWZpZWxkLW1lZGl1bSB7XG5cdFx0PiAud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LWNvbnRhaW5lciB7XG5cdFx0XHRtYXgtd2lkdGg6ICRmaWVsZF9zaXplX21lZGl1bTtcblx0XHR9XG5cdH1cbn1cblxuQG1peGluIG9yZGVyLXN1bW1hcnktZmFuY3kge1xuXG5cdHRhYmxlLndwZm9ybXMtb3JkZXItc3VtbWFyeS1wcmV2aWV3IHtcblx0XHRib3JkZXItcmFkaXVzOiA0cHg7XG5cdFx0Ym9yZGVyOiAxcHggc29saWQgI2UyZTJlMjtcblx0XHRib3JkZXItY29sbGFwc2U6IHNlcGFyYXRlO1xuXG5cdFx0dHIge1xuXG5cdFx0XHR0ZCB7XG5cdFx0XHRcdGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTJlMmUyO1xuXHRcdFx0XHRib3JkZXItYm90dG9tOiBub25lO1xuXHRcdFx0XHRib3JkZXItbGVmdDogbm9uZTtcblx0XHRcdFx0Ym9yZGVyLXJpZ2h0OiBub25lO1xuXHRcdFx0fVxuXG5cdFx0XHR0aCB7XG5cdFx0XHRcdGZvbnQtd2VpZ2h0OiA0MDA7XG5cdFx0XHRcdGJvcmRlcjogbm9uZTtcblx0XHRcdH1cblxuXHRcdFx0dGQsIHRoIHtcblx0XHRcdFx0cGFkZGluZzogOXB4IDA7XG5cdFx0XHRcdGxpbmUtaGVpZ2h0OiAyMHB4O1xuXHRcdFx0XHRiYWNrZ3JvdW5kOiBub25lO1xuXG5cdFx0XHRcdC8vIFByb2R1Y3QgTmFtZSAoSXRlbSkgY29sdW1uLlxuXHRcdFx0XHQmLndwZm9ybXMtb3JkZXItc3VtbWFyeS1pdGVtLWxhYmVsIHtcblx0XHRcdFx0XHR0ZXh0LXdyYXA6IGJhbGFuY2U7XG5cdFx0XHRcdFx0cGFkZGluZy1sZWZ0OiAxMHB4O1xuXG5cdFx0XHRcdFx0LnJ0bCAmIHtcblx0XHRcdFx0XHRcdHBhZGRpbmctcmlnaHQ6IDEwcHg7XG5cdFx0XHRcdFx0XHRwYWRkaW5nLWxlZnQ6IDA7XG5cdFx0XHRcdFx0fVxuXHRcdFx0XHR9XG5cblx0XHRcdFx0Ly8gUHJpY2UgY29sdW1uLlxuXHRcdFx0XHQmLndwZm9ybXMtb3JkZXItc3VtbWFyeS1pdGVtLXByaWNlIHtcblx0XHRcdFx0XHRwYWRkaW5nLXJpZ2h0OiAxMHB4O1xuXG5cdFx0XHRcdFx0LnJ0bCAmIHtcblx0XHRcdFx0XHRcdHBhZGRpbmctbGVmdDogMTBweDtcblx0XHRcdFx0XHRcdHBhZGRpbmctcmlnaHQ6IDA7XG5cdFx0XHRcdFx0fVxuXHRcdFx0XHR9XG5cdFx0XHR9XG5cblx0XHRcdC8vIFBsYWNlaG9sZGVyIHJvdy5cblx0XHRcdCYud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LXBsYWNlaG9sZGVyIHRkIHtcblx0XHRcdFx0cGFkZGluZy1sZWZ0OiAxMHB4O1xuXG5cdFx0XHRcdC5ydGwgJiB7XG5cdFx0XHRcdFx0cGFkZGluZy1yaWdodDogMTBweDtcblx0XHRcdFx0XHRwYWRkaW5nLWxlZnQ6IDA7XG5cdFx0XHRcdH1cblx0XHRcdH1cblxuXHRcdFx0Ly8gU3VidG90YWwgJiBUb3RhbCByb3dzLlxuXHRcdFx0Ji53cGZvcm1zLW9yZGVyLXN1bW1hcnktcHJldmlldy1zdWJ0b3RhbCB0ZCxcblx0XHRcdCYud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LXByZXZpZXctdG90YWwgdGQge1xuXHRcdFx0XHRmb250LXdlaWdodDogNzAwO1xuXHRcdFx0fVxuXHRcdH1cblx0fVxufVxuIiwiLyogQmFzZSAqL1xuLmJvZHktaW5uZXIge1xuXHRwYWRkaW5nLXRvcDogNTBweDtcblx0cGFkZGluZy1ib3R0b206IDUwcHg7XG59XG5cbi53cmFwcGVyIHtcblx0bWF4LXdpZHRoOiA2NjBweDtcbn1cblxuLndyYXBwZXItaW5uZXIge1xuXHRiYWNrZ3JvdW5kLWNvbG9yOiAkYmFja2dyb3VuZENvbnRlbnQ7XG5cdGJvcmRlcjogMXB4IHNvbGlkIGxpZ2h0ZW4oJGZvbnRDb2xvciwgNjAlKTtcblx0cGFkZGluZzogMjVweCAzMHB4IDUwcHggMzBweDtcbn1cblxuLmhlYWRlciB7XG5cdHRleHQtYWxpZ246IGNlbnRlcjtcblx0cGFkZGluZzogMCAwIDUwcHggMDtcblxuXHQuaGVhZGVyLWltYWdlIHtcblx0XHQvKiBUaGlzIGlzIG5lZWRlZCB0byBjZW50ZXIgdGhlIGxvZ28gaW4gT3V0bG9vay4gKi9cblx0XHRtYXJnaW46IDAgYXV0byAwIGF1dG87XG5cdH1cbn1cblxuLmZvb3RlciB7XG5cdGZvbnQtc2l6ZTogMTJweDtcblx0bGluZS1oZWlnaHQ6IDI0cHg7XG5cdHRleHQtYWxpZ246IGNlbnRlcjtcblx0cGFkZGluZy10b3A6IDI1cHg7XG59XG5cbi8qIFR5cG9ncmFwaHkgKi9cbmJvZHksXG50YWJsZS5ib2R5LFxuaDEsXG5oMixcbmgzLFxuaDQsXG5oNSxcbmg2LFxucCxcbnRkOm5vdCguaGVhZGVyKSxcbnRoLFxuYSB7XG5cdGxpbmUtaGVpZ2h0OiAyMnB4O1xufVxuXG4vKiBDb250ZW50ICovXG4uY29udGVudCB7XG5cdC5maWVsZC1uYW1lIHtcblx0XHRwYWRkaW5nLXRvcDogMjVweDtcblx0XHRwYWRkaW5nLWJvdHRvbTogJG1hcmdpbkJvdHRvbTtcblx0fVxuXG5cdC5maWVsZC12YWx1ZSB7XG5cdFx0cGFkZGluZy1ib3R0b206IDI1cHg7XG5cdFx0Ym9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIGxpZ2h0ZW4oJGZvbnRDb2xvciwgNjUlKTtcblx0fVxuXG5cdC5maWVsZC1uYW1lLmZpZWxkLXZhbHVlIHtcblx0XHRsaW5lLWhlaWdodDogMjJweDtcblx0fVxufVxuXG4vLyBPcmRlciBzdW1tYXJ5IHRhYmxlLlxuLmZpZWxkLXBheW1lbnQtdG90YWwgLndwZm9ybXMtb3JkZXItc3VtbWFyeS1jb250YWluZXIgdGFibGUud3Bmb3Jtcy1vcmRlci1zdW1tYXJ5LXByZXZpZXcge1xuXHRib3JkZXItcmFkaXVzOiAwO1xufVxuXG4ud3Bmb3Jtcy1sYXlvdXQtdGFibGUge1xuXHQ+IHRkIHtcblx0XHRib3JkZXItYm90dG9tOiAxcHggc29saWQgbGlnaHRlbigkZm9udENvbG9yLCA2NSUpO1xuXHR9XG5cblx0Ji1kaXNwbGF5LWJsb2Nrcyxcblx0Ji1kaXNwbGF5LWNvbHVtbnMge1xuXHRcdC53cGZvcm1zLWxheW91dC10YWJsZS1yb3cge1xuXHRcdFx0dGQge1xuXHRcdFx0XHQmLmZpZWxkLXZhbHVlIHtcblx0XHRcdFx0XHRwYWRkaW5nLWJvdHRvbTogMDtcblx0XHRcdFx0fVxuXHRcdFx0fVxuXHRcdH1cblx0fVxufVxuIiwiLndwZm9ybXMtbGF5b3V0LXRhYmxlIHtcblxuXHQ+IHRkIHtcblx0XHRwYWRkaW5nLWJvdHRvbTogMjVweDtcblx0fVxuXG5cdC53cGZvcm1zLWxheW91dC10YWJsZS1yb3cge1xuXHRcdHdpZHRoOiAxMDAlO1xuXG5cdFx0LmZpZWxkLXZhbHVlIHtcblx0XHRcdHBhZGRpbmctYm90dG9tOiAyNXB4O1xuXHRcdH1cblxuXHRcdD4gdHIgPiB0ZCB7XG5cdFx0XHRwYWRkaW5nLXJpZ2h0OiAyMHB4O1xuXG5cdFx0XHQmOmxhc3QtY2hpbGQge1xuXHRcdFx0XHRwYWRkaW5nLXJpZ2h0OiAwO1xuXHRcdFx0fVxuXHRcdH1cblx0fVxuXG5cdCYtZGlzcGxheS1yb3dzIHtcblx0XHQud3Bmb3Jtcy1sYXlvdXQtdGFibGUtcm93IHtcblx0XHRcdCY6bm90KC53cGZvcm1zLWZpcnN0LXJvdykge1xuXHRcdFx0XHR0ZC5maWVsZC1uYW1lIHtcblx0XHRcdFx0XHRkaXNwbGF5OiBub25lO1xuXHRcdFx0XHR9XG5cdFx0XHR9XG5cblx0XHRcdC5maWVsZC12YWx1ZSB7XG5cdFx0XHRcdHBhZGRpbmctYm90dG9tOiAxNXB4O1xuXHRcdFx0fVxuXG5cdFx0XHQmOmxhc3QtY2hpbGQge1xuXHRcdFx0XHQuZmllbGQtdmFsdWUge1xuXHRcdFx0XHRcdHBhZGRpbmctYm90dG9tOiAwO1xuXHRcdFx0XHR9XG5cdFx0XHR9XG5cdFx0fVxuXHR9XG5cblx0Ji1kaXNwbGF5LWJsb2Nrcyxcblx0Ji1kaXNwbGF5LWNvbHVtbnMge1xuXHRcdC53cGZvcm1zLWxheW91dC10YWJsZS1yb3cge1xuXHRcdFx0dHI6bGFzdC1jaGlsZCB7XG5cdFx0XHRcdC53cGZvcm1zLWxheW91dC10YWJsZS1jZWxsIHtcblx0XHRcdFx0XHQuZmllbGQtdmFsdWUge1xuXHRcdFx0XHRcdFx0cGFkZGluZy1ib3R0b206IDA7XG5cdFx0XHRcdFx0fVxuXHRcdFx0XHR9XG5cdFx0XHR9XG5cdFx0fVxuXHR9XG5cblx0LndwZm9ybXMtbGF5b3V0LXRhYmxlLWNlbGwge1xuXHRcdHRkIHtcblx0XHRcdGJvcmRlcjogMCAhaW1wb3J0YW50O1xuXHRcdH1cblxuXHRcdCYud3Bmb3Jtcy13aWR0aC0yNSxcblx0XHQmLndwZm9ybXMtd2lkdGgtMzMge1xuXHRcdFx0LmZpZWxkLXBheW1lbnQtdG90YWwge1xuXHRcdFx0XHQud3Bmb3Jtcy1wYXltZW50LXRvdGFsIHtcblx0XHRcdFx0XHRkaXNwbGF5OiBibG9jayAhaW1wb3J0YW50O1xuXHRcdFx0XHR9XG5cblx0XHRcdFx0LndwZm9ybXMtb3JkZXItc3VtbWFyeS1wcmV2aWV3IHtcblx0XHRcdFx0XHRkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG5cdFx0XHRcdH1cblx0XHRcdH1cblx0XHR9XG5cdH1cbn1cblxuLmZpZWxkLXBheW1lbnQtdG90YWwge1xuXHQud3Bmb3Jtcy1wYXltZW50LXRvdGFsIHtcblx0XHRkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG5cdH1cbn1cbiIsIi8vIFdQRm9ybXMgUERGIHN0eWxlcy5cbi8vXG4vLyBDb21tb24gcGFydCBmb3IgYWxsIHRoZSB0ZW1wbGF0ZXMuXG4vL1xuLy8gQHNpbmNlIDEuMC4wXG5cbkBpbXBvcnQgXCJ2YXJzXCI7XG5AaW1wb3J0IFwiZm9udHNcIjtcblxuLy8gUGFnZSBzZXR0aW5ncy5cbkBwYWdlIHtcblx0bWFyZ2luOiAwO1xuXHRwYWRkaW5nOiAwO1xufVxuXG5ib2R5IHtcblx0bWFyZ2luOiAkYm9keV9tYXJnaW4gKyAkY29udGFpbmVyX3BhZGRpbmcgMDsgLy8gTWFyZ2luIGZvciB0aGUgc3BhY2Ugb24gdGhlIG5leHQgcGFnZXNcbiAgICBwYWRkaW5nOiAwO1xuICAgIHRleHQtYWxpZ246IGxlZnQ7XG4gICAgYmFja2dyb3VuZDogI2ZmZmZmZjtcblxuICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcblx0XHRkaXNwbGF5OiBub25lO1xuXHR9XG5cblx0Jjo6LXdlYmtpdC1zY3JvbGxiYXItYnV0dG9uIHtcblx0XHRkaXNwbGF5OiBub25lO1xuXHR9XG5cblx0KiB7XG5cdFx0b3V0bGluZS1jb2xvcjogdHJhbnNwYXJlbnQ7XG5cdFx0b3V0bGluZS1vZmZzZXQ6IDEycHg7XG5cdH1cbn1cblxuYm9keSwgdGFibGUsIGgxLCBoMiwgaDMsIGg0LCBoNSwgaDYsIHAsIHRkLCB0aCwgYSB7XG4gIGZvbnQtZmFtaWx5OiAkZm9udF9zYW5zO1xufVxuXG4vLyBQYWdlIGJhY2tncm91bmQuXG4ucGFnZS1iYWNrZ3JvdW5kIHtcbiAgICBwb3NpdGlvbjogZml4ZWQ7IC8vIEZpeGVkIHBvc2l0aW9uIGlzIHJlcXVpcmVkIHRvIHJlcGVhdCB0aGUgYmFja2dyb3VuZCBvbiBlYWNoIHBhZ2UgaW4gdGhlIFBERi5cbiAgICB0b3A6IDA7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgaGVpZ2h0OiAxMDAlO1xuICAgIHotaW5kZXg6IC0xO1xufVxuXG4vLyBDb250YWluZXIgYmFja2dyb3VuZC5cbi5jb250YWluZXItYmFja2dyb3VuZCB7XG4gICAgcG9zaXRpb246IGZpeGVkOyAvLyBGaXhlZCBwb3NpdGlvbiBpcyByZXF1aXJlZCB0byByZXBlYXQgdGhlIGNvbnRhaW5lciBvbiBlYWNoIHBhZ2UgaW4gdGhlIFBERi5cbiAgICB0b3A6ICRib2R5X21hcmdpbjtcbiAgICBsZWZ0OiAkYm9keV9tYXJnaW47XG4gICAgcmlnaHQ6ICRib2R5X21hcmdpbjtcbiAgICBib3R0b206ICRib2R5X21hcmdpbjtcbiAgICB6LWluZGV4OiAtMTtcblxuXHQuY29udGFpbmVyLWJhY2tncm91bmQtd3JhcCB7XG5cdFx0cG9zaXRpb246IHJlbGF0aXZlO1xuXHRcdHdpZHRoOiAxMDAlO1xuXHRcdGhlaWdodDogMTAwJTtcblx0fVxuXG5cdC5jb250YWluZXItYmFja2dyb3VuZC1maWxsIHtcblx0XHRwb3NpdGlvbjogYWJzb2x1dGU7XG5cdFx0dG9wOiAwO1xuXHRcdGxlZnQ6IDA7XG5cdFx0cmlnaHQ6IDA7XG5cdFx0Ym90dG9tOiAwO1xuXHR9XG5cblx0LmNvbnRhaW5lci1zaGFkb3cge1xuXHRcdHBvc2l0aW9uOiBhYnNvbHV0ZTtcblx0XHR0b3A6IDA7XG5cdFx0bGVmdDogMDtcblx0XHRyaWdodDogMDtcblx0XHRib3R0b206IDA7XG5cdFx0ei1pbmRleDogLTE7XG5cblx0XHRiYWNrZ3JvdW5kLWltYWdlOiBub25lO1xuXHRcdGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlO1xuXHRcdGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XG5cblx0XHQmW2RhdGEtc2hhZG93PVwic21hbGxcIl0ge1xuXHRcdFx0YmFja2dyb3VuZC1pbWFnZTogdXJsKCBcIntXUEZPUk1TX1BERl9VUkx9YXNzZXRzL2ltYWdlcy9zaGFkb3cvc2hhZG93LXNtLnBuZ1wiICk7XG5cdFx0XHRsZWZ0OiAtNXB4O1xuXHRcdFx0cmlnaHQ6IC01cHg7XG5cdFx0XHRib3R0b206IC0xMHB4O1xuXHRcdH1cblxuXHRcdCZbZGF0YS1zaGFkb3c9XCJtZWRpdW1cIl0ge1xuXHRcdFx0YmFja2dyb3VuZC1pbWFnZTogdXJsKCBcIntXUEZPUk1TX1BERl9VUkx9YXNzZXRzL2ltYWdlcy9zaGFkb3cvc2hhZG93LW1kLnBuZ1wiICk7XG5cdFx0XHRsZWZ0OiAtMTBweDtcblx0XHRcdHJpZ2h0OiAtMTBweDtcblx0XHRcdGJvdHRvbTogLTIwcHg7XG5cdFx0fVxuXG5cdFx0JltkYXRhLXNoYWRvdz1cImxhcmdlXCJdIHtcblx0XHRcdGJhY2tncm91bmQtaW1hZ2U6IHVybCggXCJ7V1BGT1JNU19QREZfVVJMfWFzc2V0cy9pbWFnZXMvc2hhZG93L3NoYWRvdy1sZy5wbmdcIiApO1xuXHRcdFx0bGVmdDogLTIwcHg7XG5cdFx0XHRyaWdodDogLTIwcHg7XG5cdFx0XHRib3R0b206IC0zNXB4O1xuXHRcdH1cblx0fVxufVxuXG4vLyBDb250YWluZXIgYmFja2dyb3VuZCB3cmFwcGVyLiBTb21ldGltZXMgd2Ugc2hvdWxkIGhhdmUgYSB3cmFwcGVyIHRvIHB1dCB0aGUgaGVhZGVyIGluc2lkZS5cbi5jb250YWluZXItYmFja2dyb3VuZC13cmFwcGVyIHtcblx0cG9zaXRpb246IGZpeGVkO1xuXHR0b3A6ICRib2R5X21hcmdpbjtcbiAgICBsZWZ0OiAkYm9keV9tYXJnaW47XG4gICAgcmlnaHQ6ICRib2R5X21hcmdpbjtcbiAgICBib3R0b206ICRib2R5X21hcmdpbjtcbiAgICB6LWluZGV4OiAtMTtcblxuICAgIC5jb250YWluZXItYmFja2dyb3VuZCB7XG4gICAgXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdH1cbn1cblxuLy8gQ29udGVudCBjb250YWluZXIuXG4uY29udGFpbmVyLWNvbnRlbnQge1xuXHRtYXJnaW46IC0kY29udGFpbmVyX3BhZGRpbmcgJGJvZHlfbWFyZ2luO1xuXHRwYWRkaW5nOiAkY29udGFpbmVyX3BhZGRpbmc7XG59XG5cbi5jb250ZW50IHtcblx0bWluLWhlaWdodDogMzBweDtcblxuXHR0YWJsZS5ib2R5LCBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2LCBwLCB0ZDpub3QoLmhlYWRlciksIHRoLCBhIHtcblx0XHRsaW5lLWhlaWdodDogMS4zO1xuXHR9XG5cblx0dGFibGUge1xuXHRcdHdpZHRoOiAxMDAlO1xuXHR9XG5cblx0dWwgPiBsaSB7XG5cdFx0bGlzdC1zdHlsZS10eXBlOiBkaXNjO1xuXHR9XG5cblx0b2wgPiBsaSB7XG5cdFx0bGlzdC1zdHlsZS10eXBlOiBkZWNpbWFsO1xuXHR9XG5cblx0LmZpZWxkLXJhdGluZyAuZmllbGQtdmFsdWUge1xuXHRcdHdoaXRlLXNwYWNlOiBub3dyYXA7XG5cblx0XHRpbWcge1xuXHRcdFx0ZGlzcGxheTogaW5saW5lICFpbXBvcnRhbnQ7XG5cdFx0XHRmbG9hdDogbm9uZTtcblx0XHRcdHdpZHRoOiAxNnB4O1xuXHRcdFx0aGVpZ2h0OiAxNnB4O1xuXHRcdFx0bWFyZ2luLXJpZ2h0OiA1cHg7XG5cdFx0fVxuXHR9XG5cblx0LmZpZWxkLXJlcGVhdGVyLW5hbWUge1xuXHRcdGZvbnQtc2l6ZTogMThweDtcblx0fVxufVxuXG4uZGl2aWRlciB7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDFweDtcblx0YmFja2dyb3VuZC1jb2xvcjogIzQ0NDAzQztcblx0b3BhY2l0eTogMC4xNTtcblx0bWFyZ2luOiA0MHB4IDA7XG59XG5cbi5oZWFkZXIge1xuXHRkaXNwbGF5OiBibG9jaztcblx0cGFkZGluZzogMDtcblxuXHQmW2RhdGEtYWxpZ249XCJsZWZ0XCJdIHtcblx0XHR0ZXh0LWFsaWduOiBsZWZ0O1xuXHR9XG5cblx0JltkYXRhLWFsaWduPVwiY2VudGVyXCJdIHtcblx0XHR0ZXh0LWFsaWduOiBjZW50ZXI7XG5cdH1cblxuXHQmW2RhdGEtYWxpZ249XCJyaWdodFwiXSB7XG5cdFx0dGV4dC1hbGlnbjogcmlnaHQ7XG5cdH1cbn1cblxuW2RhdGEtc2l6ZT1cInNtYWxsXCJdIC5sb2dvIHtcblx0bWF4LWhlaWdodDogNzBweDtcblx0bWF4LXdpZHRoOiAzMyU7XG59XG5cbltkYXRhLXNpemU9XCJtZWRpdW1cIl0gLmxvZ28ge1xuXHRtYXgtaGVpZ2h0OiAxMjBweDtcblx0bWF4LXdpZHRoOiAzNDBweDtcbn1cblxuW2RhdGEtc2l6ZT1cImxhcmdlXCJdIC5sb2dvIHtcblx0bWF4LWhlaWdodDogMTcwcHg7XG5cdG1heC13aWR0aDogNjYlO1xufVxuXG5bZGF0YS1zaXplPVwiZnVsbFwiXSAubG9nbyB7XG5cdG1heC1oZWlnaHQ6IDM2MHB4O1xuXHRtYXgtd2lkdGg6IDEwMCU7XG59XG5cbi5sb2dvIHtcblx0JltzcmM9XCJcIl0ge1xuXHRcdGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcblx0fVxufVxuXG4uc2lnbmF0dXJlIHtcblx0JltkYXRhLXR5cGU9XCJ0ZXh0XCJdIHtcblx0XHQuc2lnbmF0dXJlX2ltYWdlIHtcblx0XHRcdGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcblx0XHR9XG5cdH1cblxuXHQmW2RhdGEtdHlwZT1cImltYWdlXCJdIHtcblx0XHQuc2lnbmF0dXJlX3RleHQge1xuXHRcdFx0ZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xuXHRcdH1cblx0fVxuXG5cdCZfaW1hZ2Vbc3JjPVwiXCJdIHtcblx0XHRkaXNwbGF5OiBub25lO1xuXHR9XG5cblx0Jl9pbWFnZVtkYXRhLXNpemU9XCJzbWFsbFwiXSB7XG5cdFx0bWF4LXdpZHRoOiAxNDBweDtcbiAgICBcdG1heC1oZWlnaHQ6IDcwcHg7XG5cdH1cblxuXHQmX2ltYWdlW2RhdGEtc2l6ZT1cIm1lZGl1bVwiXSB7XG5cdFx0bWF4LXdpZHRoOiAxODBweDtcbiAgICBcdG1heC1oZWlnaHQ6IDEwMHB4O1xuXHR9XG5cblx0Jl9pbWFnZVtkYXRhLXNpemU9XCJsYXJnZVwiXSB7XG5cdFx0bWF4LXdpZHRoOiAyMjBweDtcbiAgICBcdG1heC1oZWlnaHQ6IDEzMHB4O1xuXHR9XG5cblx0Jl9pbWFnZVtkYXRhLXNpemU9XCJmdWxsXCJdIHtcblx0XHRtYXgtd2lkdGg6IDEwMCU7XG4gICAgXHRtYXgtaGVpZ2h0OiAzMDBweDtcblx0fVxufVxuXG4uZm9vdGVyIHtcblx0ZGlzcGxheTogYmxvY2s7XG59XG5cbi5wcmV2aWV3LWhpZ2hsaWdodCB7XG5cdG91dGxpbmU6IDRweCBzb2xpZCAjRDYzNjM4O1xuXHRvdXRsaW5lLW9mZnNldDogNnB4O1xuXHR0cmFuc2l0aW9uOiBvdXRsaW5lLWNvbG9yIDI1MG1zIGVhc2Utb3V0LCBvdXRsaW5lLW9mZnNldCAyNTBtcyBlYXNlLW91dDtcblxuXHQmLnBhZ2UtYmFja2dyb3VuZCB7XG5cdFx0b3V0bGluZS1vZmZzZXQ6IC00cHg7XG5cdFx0Ym9yZGVyLXJhZGl1czogMTBweDtcblx0fVxufVxuXG4uaGlkZGVuIHtcblx0ZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xufVxuXG4ud2lkdGgtNTAge1xuXHR3aWR0aDogNTAlO1xufVxuXG4uYmlsbGluZ19jb250ZW50LCAuYnVzaW5lc3NfYWRkcmVzcywgLmJ1c2luZXNzX25hbWUsIC5kZXRhaWxzX2NvbnRlbnQsIC5oZWFkZXJfYWRkcmVzcyxcbi5oZWFkZXJfYWRkcmVzc18yLCAucGFyYWdyYXBoXzEsIC5wYXltZW50X2NvbnRlbnQsIC50ZXJtc19jb250ZW50LCAuc2lnbmF0dXJlX3N1YmhlYWRpbmcsIC5kYXRlX3N1YmhlYWRpbmcgIHtcblx0d29yZC1icmVhazogYnJlYWstd29yZDtcbn1cblxuLmhlYWRlcl9lbWFpbCwgLmhlYWRlcl9waG9uZSwgLmJhZGdlX3N1YmhlYWRpbmcsIC5iYWRnZV95ZWFyLCAuYmlsbGluZ19oZWFkaW5nLCAuYnVzaW5lc3NfZW1haWwsXG4uYnVzaW5lc3NfcGhvbmUsIC5kYXRlLCAuZGV0YWlsc19oZWFkaW5nLCAuZHVlX2RhdGUsIC5kdWVfZGF0ZV9oZWFkaW5nLFxuLmhlYWRlcl9lbWFpbCwgLmhlYWRlcl9waG9uZSwgLmhlYWRpbmdfMSwgLmhlYWRpbmdfMiwgLmhlYWRpbmdfMywgLmludm9pY2VfbnVtYmVyLFxuLmludm9pY2VfbnVtYmVyX2hlYWRpbmcsIC5jbmFtZSwgLnBheW1lbnRfaGVhZGluZywgLnBvc3RlZF9kYXRlLCAucG9zdGVkX2RhdGVfaGVhZGluZywgLnRheF9oZWFkaW5nLCAudGF4X2lkLCAudGVybXNfaGVhZGluZyB7XG5cdHdoaXRlLXNwYWNlOiBub3dyYXA7XG59XG5cbiIsIi8vIFdQRm9ybXMgUERGIHN0eWxlcy5cbi8vXG4vLyBWYXJpYWJsZXMgZm9yIGFsbCB0ZW1wbGF0ZXMuXG4vL1xuLy8gQHNpbmNlIDEuMC4wXG5cbiRib2R5X21hcmdpbjogNDVweDtcbiRjb250YWluZXJfcGFkZGluZzogNjBweDtcblxuJGZvbnRfc2VyaWY6IFwiTGl0ZXJhdGFcIiwgc2VyaWY7XG4kZm9udF9zYW5zOiBcIkludGVyXCIsIHNhbnMtc2VyaWY7XG4kZm9udF9jdXJzaXZlOiBcIkxvYnN0ZXJcIiwgY3Vyc2l2ZTtcbiIsIi8vIFdQRm9ybXMgUERGIHN0eWxlcy5cbi8vXG4vLyBDb21tb24gcGFydCBmb3IgYWxsIHRoZSB0ZW1wbGF0ZXMuXG4vL1xuLy8gQHNpbmNlIDEuMC4wXG5cbiR3cGZvcm1zX3BkZl91cmw6ICd7V1BGT1JNU19QREZfVVJMfSc7XG5cbi8vIFNhbnMgU2VyaWY6IEludGVyLlxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnSW50ZXInO1xuXHRzcmM6IHVybCggJHdwZm9ybXNfcGRmX3VybCArICdhc3NldHMvZm9udHMvSW50ZXIvSW50ZXItUmVndWxhci50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiBub3JtYWw7XG5cdGZvbnQtc3R5bGU6IG5vcm1hbDtcbn1cblxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnSW50ZXInO1xuXHRzcmM6IHVybCggJHdwZm9ybXNfcGRmX3VybCArICdhc3NldHMvZm9udHMvSW50ZXIvSW50ZXItQm9sZC50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiBib2xkO1xuXHRmb250LXN0eWxlOiBub3JtYWw7XG59XG5cbkBmb250LWZhY2Uge1xuXHRmb250LWZhbWlseTogJ0ludGVyJztcblx0c3JjOiB1cmwoICR3cGZvcm1zX3BkZl91cmwgKyAnYXNzZXRzL2ZvbnRzL0ludGVyL0ludGVyLUl0YWxpYy50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiBub3JtYWw7XG5cdGZvbnQtc3R5bGU6IGl0YWxpYztcbn1cblxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnSW50ZXInO1xuXHRzcmM6IHVybCggJHdwZm9ybXNfcGRmX3VybCArICdhc3NldHMvZm9udHMvSW50ZXIvSW50ZXItQm9sZEl0YWxpYy50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiBib2xkO1xuXHRmb250LXN0eWxlOiBpdGFsaWM7XG59XG5cbkBmb250LWZhY2Uge1xuXHRmb250LWZhbWlseTogJ0ludGVyJztcblx0c3JjOiB1cmwoICR3cGZvcm1zX3BkZl91cmwgKyAnYXNzZXRzL2ZvbnRzL0ludGVyL0ludGVyLUV4dHJhQm9sZC50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiA4MDA7XG5cdGZvbnQtc3R5bGU6IG5vcm1hbDtcbn1cblxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnSW50ZXInO1xuXHRzcmM6IHVybCggJHdwZm9ybXNfcGRmX3VybCArICdhc3NldHMvZm9udHMvSW50ZXIvSW50ZXItRXh0cmFCb2xkSXRhbGljLnR0ZicgKSBmb3JtYXQoICd0cnVldHlwZScgKTtcblx0Zm9udC13ZWlnaHQ6IDgwMDtcblx0Zm9udC1zdHlsZTogaXRhbGljO1xufVxuXG5AZm9udC1mYWNlIHtcblx0Zm9udC1mYW1pbHk6ICdJbnRlcic7XG5cdHNyYzogdXJsKCAkd3Bmb3Jtc19wZGZfdXJsICsgJ2Fzc2V0cy9mb250cy9JbnRlci9JbnRlci1CbGFjay50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiA5MDA7XG5cdGZvbnQtc3R5bGU6IG5vcm1hbDtcbn1cblxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnSW50ZXInO1xuXHRzcmM6IHVybCggJHdwZm9ybXNfcGRmX3VybCArICdhc3NldHMvZm9udHMvSW50ZXIvSW50ZXItQmxhY2tJdGFsaWMudHRmJyApIGZvcm1hdCggJ3RydWV0eXBlJyApO1xuXHRmb250LXdlaWdodDogOTAwO1xuXHRmb250LXN0eWxlOiBpdGFsaWM7XG59XG5cbi8vIFNlcmlmOiBMaXRlcmF0YS5cbkBmb250LWZhY2Uge1xuXHRmb250LWZhbWlseTogJ0xpdGVyYXRhJztcblx0c3JjOiB1cmwoICR3cGZvcm1zX3BkZl91cmwgKyAnYXNzZXRzL2ZvbnRzL0xpdGVyYXRhL0xpdGVyYXRhLVJlZ3VsYXIudHRmJyApIGZvcm1hdCggJ3RydWV0eXBlJyApO1xuXHRmb250LXdlaWdodDogbm9ybWFsO1xuXHRmb250LXN0eWxlOiBub3JtYWw7XG59XG5cbkBmb250LWZhY2Uge1xuXHRmb250LWZhbWlseTogJ0xpdGVyYXRhJztcblx0c3JjOiB1cmwoICR3cGZvcm1zX3BkZl91cmwgKyAnYXNzZXRzL2ZvbnRzL0xpdGVyYXRhL0xpdGVyYXRhLUl0YWxpYy50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiBub3JtYWw7XG5cdGZvbnQtc3R5bGU6IGl0YWxpYztcbn1cblxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnTGl0ZXJhdGEnO1xuXHRzcmM6IHVybCggJHdwZm9ybXNfcGRmX3VybCArICdhc3NldHMvZm9udHMvTGl0ZXJhdGEvTGl0ZXJhdGEtTWVkaXVtLnR0ZicgKSBmb3JtYXQoICd0cnVldHlwZScgKTtcblx0Zm9udC13ZWlnaHQ6IDUwMDtcblx0Zm9udC1zdHlsZTogbm9ybWFsO1xufVxuXG5AZm9udC1mYWNlIHtcblx0Zm9udC1mYW1pbHk6ICdMaXRlcmF0YSc7XG5cdHNyYzogdXJsKCAkd3Bmb3Jtc19wZGZfdXJsICsgJ2Fzc2V0cy9mb250cy9MaXRlcmF0YS9MaXRlcmF0YS1NZWRpdW1JdGFsaWMudHRmJyApIGZvcm1hdCggJ3RydWV0eXBlJyApO1xuXHRmb250LXdlaWdodDogNTAwO1xuXHRmb250LXN0eWxlOiBpdGFsaWM7XG59XG5cbkBmb250LWZhY2Uge1xuXHRmb250LWZhbWlseTogJ0xpdGVyYXRhJztcblx0c3JjOiB1cmwoICR3cGZvcm1zX3BkZl91cmwgKyAnYXNzZXRzL2ZvbnRzL0xpdGVyYXRhL0xpdGVyYXRhLUJvbGQudHRmJyApIGZvcm1hdCggJ3RydWV0eXBlJyApO1xuXHRmb250LXdlaWdodDogYm9sZDtcblx0Zm9udC1zdHlsZTogbm9ybWFsO1xufVxuXG5AZm9udC1mYWNlIHtcblx0Zm9udC1mYW1pbHk6ICdMaXRlcmF0YSc7XG5cdHNyYzogdXJsKCAkd3Bmb3Jtc19wZGZfdXJsICsgJ2Fzc2V0cy9mb250cy9MaXRlcmF0YS9MaXRlcmF0YS1Cb2xkSXRhbGljLnR0ZicgKSBmb3JtYXQoICd0cnVldHlwZScgKTtcblx0Zm9udC13ZWlnaHQ6IGJvbGQ7XG5cdGZvbnQtc3R5bGU6IGl0YWxpYztcbn1cblxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnTGl0ZXJhdGEnO1xuXHRzcmM6IHVybCggJHdwZm9ybXNfcGRmX3VybCArICdhc3NldHMvZm9udHMvTGl0ZXJhdGEvTGl0ZXJhdGEtRXh0cmFCb2xkLnR0ZicgKSBmb3JtYXQoICd0cnVldHlwZScgKTtcblx0Zm9udC13ZWlnaHQ6IDgwMDtcblx0Zm9udC1zdHlsZTogbm9ybWFsO1xufVxuXG5AZm9udC1mYWNlIHtcblx0Zm9udC1mYW1pbHk6ICdMaXRlcmF0YSc7XG5cdHNyYzogdXJsKCAkd3Bmb3Jtc19wZGZfdXJsICsgJ2Fzc2V0cy9mb250cy9MaXRlcmF0YS9MaXRlcmF0YS1FeHRyYUJvbGRJdGFsaWMudHRmJyApIGZvcm1hdCggJ3RydWV0eXBlJyApO1xuXHRmb250LXdlaWdodDogODAwO1xuXHRmb250LXN0eWxlOiBpdGFsaWM7XG59XG5cbkBmb250LWZhY2Uge1xuXHRmb250LWZhbWlseTogJ0xpdGVyYXRhJztcblx0c3JjOiB1cmwoICR3cGZvcm1zX3BkZl91cmwgKyAnYXNzZXRzL2ZvbnRzL0xpdGVyYXRhL0xpdGVyYXRhLUJsYWNrLnR0ZicgKSBmb3JtYXQoICd0cnVldHlwZScgKTtcblx0Zm9udC13ZWlnaHQ6IDkwMDtcblx0Zm9udC1zdHlsZTogbm9ybWFsO1xufVxuXG5AZm9udC1mYWNlIHtcblx0Zm9udC1mYW1pbHk6ICdMaXRlcmF0YSc7XG5cdHNyYzogdXJsKCAkd3Bmb3Jtc19wZGZfdXJsICsgJ2Fzc2V0cy9mb250cy9MaXRlcmF0YS9MaXRlcmF0YS1CbGFja0l0YWxpYy50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiA5MDA7XG5cdGZvbnQtc3R5bGU6IGl0YWxpYztcbn1cblxuLy8gU2NyaXB0OiBMb2JzdGVyLlxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiAnTG9ic3Rlcic7XG5cdHNyYzogdXJsKCAkd3Bmb3Jtc19wZGZfdXJsICsgJ2Fzc2V0cy9mb250cy9Mb2JzdGVyL0xvYnN0ZXItUmVndWxhci50dGYnICkgZm9ybWF0KCAndHJ1ZXR5cGUnICk7XG5cdGZvbnQtd2VpZ2h0OiBub3JtYWw7XG5cdGZvbnQtc3R5bGU6IG5vcm1hbDtcbn1cbiIsIi8vIFdQRm9ybXMgUERGIHN0eWxlcy5cbi8vXG4vLyBDb21tb24gcGFydCBmb3IgYWxsIHRoZSBub3RpZmljYXRpb24gdGVtcGxhdGVzLlxuLy9cbi8vIEBzaW5jZSAxLjAuMFxuXG4vLyBDb250YWluZXIgYmFja2dyb3VuZC5cbi5jb250YWluZXItYmFja2dyb3VuZC13cmFwcGVyIHtcblx0Ym9yZGVyLXJhZGl1czogJGJvcmRlcl9yYWRpdXM7XG5cblx0JiA+IC5oZWFkZXIge1xuXHRcdHZpc2liaWxpdHk6IGhpZGRlbiAhaW1wb3J0YW50O1xuXHR9XG5cbiAgICAuY29udGFpbmVyLWJhY2tncm91bmQtZmlsbCB7XG4gICAgXHR0b3A6IDA7XG4gICAgXHRyaWdodDogMDtcblx0XHRsZWZ0OiAwO1xuXHRcdGJvdHRvbTogdW5zZXQ7XG5cdFx0Ym9yZGVyLXJhZGl1czogJGJvcmRlcl9yYWRpdXM7XG5cdFx0cGFkZGluZzogJGNvbnRhaW5lcl9wYWRkaW5nO1xuXG4gICAgICAgIC8vIEhpZGUgdGhlIGNvbnRlbnQgYXJlYSBpbnNpZGUgdGhlIGNvbnRhaW5lciBiYWNrZ3JvdW5kLiBXZSBvdXRwdXQgaXQganVzdCB0byBzaXplIHRoZSBjb250YWluZXIuXG5cdFx0JiA+IC5oZWFkZXIsXG5cdFx0JiA+IC5jb250ZW50LFxuXHRcdCYgPiAuZm9vdGVyIHtcblx0XHRcdHZpc2liaWxpdHk6IGhpZGRlbiAhaW1wb3J0YW50O1xuXHRcdH1cbiAgICB9XG5cbiAgICAuY29udGFpbmVyLXNoYWRvdyB7XG5cdFx0Ym90dG9tOiB1bnNldDtcblx0fVxufVxuXG4vLyBDb250ZW50IGFyZWEgYWRqdXN0bWVudHMuXG4uY29udGVudCB7XG4gICAgdGFibGUge1xuICAgICAgICB0Ym9keSB7XG4gICAgICAgICAgICB0cjpmaXJzdC1jaGlsZCB7XG4gICAgICAgICAgICAgICAgdGQge1xuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nLXRvcDogMDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59XG5cbi5kaXZpZGVyLWludmlzaWJsZSB7XG5cdHZpc2liaWxpdHk6IGhpZGRlbjtcblx0aGVpZ2h0OiAwO1xuXHRtYXgtaGVpZ2h0OiAwO1xufVxuXG4vLyBDb250YWluZXIgYmFja2dyb3VuZC5cbmJvZHkge1xuXHRtYXJnaW4tdG9wOiAkYm9keV9tYXJnaW47XG59XG5cbi5oZWFkZXIge1xuXHRtYXJnaW46IDAgLSN7JGNvbnRhaW5lcl9wYWRkaW5nfSAjeyRjb250YWluZXJfcGFkZGluZ30gLSN7JGNvbnRhaW5lcl9wYWRkaW5nfTtcblx0cGFkZGluZzogMCAkY29udGFpbmVyX3BhZGRpbmc7XG5cblx0JltkYXRhLWxvZ289XCJcIl06aGFzKCBpbWdbc3JjPVwiXCJdICkge1xuXHRcdG1hcmdpbi1ib3R0b206IDA7XG5cdH1cblxuXHQmOmhhcyggaW1nW3NyYz1cIlwiXSApIHtcblx0XHRtYXJnaW4tYm90dG9tOiAwO1xuXHR9XG59XG5cbi5jb250YWluZXItY29udGVudCB7XG5cdC5jb250ZW50IHtcblx0XHRtYXJnaW4tdG9wOiAkY29udGFpbmVyX3BhZGRpbmc7XG5cdH1cbn1cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUdBQSxBQUFBLElBQUk7QUFDSixLQUFLLENBQUM7RUFDTCxNQUFNLEVBQUUsZUFBZTtFQUN2QixNQUFNLEVBQUUsQ0FBQztFQUNULE9BQU8sRUFBRSxDQUFDO0VBQ1YsS0FBSyxFQUFFLGVBQWU7RUFDdEIsU0FBUyxFQUFFLElBQUk7RUFDZixlQUFlLEVBQUUsVUFBVTtFQUMzQixrQkFBa0IsRUFBRSxVQUFVO0VBQzlCLFVBQVUsRUFBRSxVQUFVO0VBQ3RCLHNCQUFzQixFQUFFLHNCQUFzQjtFQUM5Qyx1QkFBdUIsRUFBRSxvQkFBb0I7Q0FDN0M7O0FBRUQsQUFBQSxHQUFHLENBQUM7RUFDSCxPQUFPLEVBQUUsSUFBSTtFQUNiLGVBQWUsRUFBRSxJQUFJO0VBQ3JCLHNCQUFzQixFQUFFLE9BQU87RUFDL0IsS0FBSyxFQUFFLElBQUk7RUFDWCxTQUFTLEVBQUUsSUFBSTtFQUNmLEtBQUssRUFBRSxJQUFJO0VBQ1gsT0FBTyxFQUFFLEtBQUs7Q0FDZDs7QUFFRCxBQUFBLENBQUMsQ0FBQyxHQUFHLENBQUM7RUFDTCxNQUFNLEVBQUUsSUFBSTtDQUNaOztBQUVELEFBQUEsQ0FBQyxDQUFDO0VBQ0QsTUFBTSxFQUFFLFVBQVU7Q0FDbEI7O0FBRUQsQUFBQSxLQUFLLENBQUM7RUFDTCxlQUFlLEVBQUUsUUFBUTtFQUN6QixjQUFjLEVBQUUsQ0FBQztDQUNqQjs7QUFFRCxBQUFBLEVBQUUsQ0FBQztFQUNGLFNBQVMsRUFBRSxVQUFVO0VBQ3JCLGVBQWUsRUFBRSxJQUFJO0VBQ3JCLFlBQVksRUFBRSxJQUFJO0VBQ2xCLE9BQU8sRUFBRSxJQUFJO0VBQ2IsZUFBZSxFQUFFLG1CQUFtQjtDQUNwQzs7QUFFRCxBQUFBLEtBQUs7QUFDTCxFQUFFO0FBQ0YsRUFBRSxDQUFDO0VBQ0YsT0FBTyxFQUFFLENBQUM7RUFDVixjQUFjLEVBQUUsR0FBRztDQUNuQjs7QUFFRCxBQUFBLEVBQUU7QUFDRixFQUFFO0FBQ0YsRUFBRTtBQUNGLEVBQUU7QUFDRixFQUFFO0FBQ0YsRUFBRSxDQUFDO0VBQ0YsTUFBTSxFQUFFLENBQUM7RUFDVCxPQUFPLEVBQUUsQ0FBQztDQUNWOztBQUVELEFBQUEsWUFBWTtBQUNaLGNBQWMsQ0FBQztFQUNkLEtBQUssRUFBRSxJQUFJO0NBQ1g7O0FBRUQsQUFBQSxjQUFjLENBQUM7RUFDZCxLQUFLLEVBQUUsSUFBSTtDQUNYOztBQUVELEFBQUEsY0FBYztBQUNkLGNBQWMsQ0FBQyxDQUFDO0FBQ2hCLGNBQWMsQ0FBQyxJQUFJO0FBQ25CLGNBQWMsQ0FBQyxJQUFJO0FBQ25CLGNBQWMsQ0FBQyxFQUFFO0FBQ2pCLGNBQWMsQ0FBQyxHQUFHLENBQUM7RUFDbEIsV0FBVyxFQUFFLElBQUk7Q0FDakI7O0FBRUQsQUFBQSxLQUFLO0FBQ0wsRUFBRSxDQUFDO0VBQ0YsZ0JBQWdCLEVBQUUsR0FBRztFQUNyQixnQkFBZ0IsRUFBRSxHQUFHO0NBQ3JCOztBQUVELEFBQUEsUUFBUSxDQUFDLENBQUMsQ0FBQztFQUNWLE9BQU8sRUFBRSxDQUFDO0NBQ1Y7O0FBRUQsQUFBQSxHQUFHLENBQUM7RUFDSCxzQkFBc0IsRUFBRSxPQUFPO0NBQy9COztBQUVELEFBQUEsSUFBSSxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLEVBQUUsVUFBVSxDQUFDO0VBQ3JDLG9CQUFvQixFQUFFLElBQUk7RUFDMUIsd0JBQXdCLEVBQUUsSUFBSTtDQUM5Qjs7QUNqR0QsQUFBQSxJQUFJO0FBQ0osS0FBSyxBQUFBLEtBQUs7QUFDVixFQUFFO0FBQ0YsRUFBRTtBQUNGLEVBQUU7QUFDRixFQUFFO0FBQ0YsRUFBRTtBQUNGLEVBQUU7QUFDRixDQUFDO0FBQ0QsRUFBRTtBQUNGLEVBQUU7QUFDRixDQUFDLENBQUM7RUFDRCxLQUFLLEVGSk0sT0FBTztFRUtsQixXQUFXLEVIWEMsYUFBYSxFQUFFLGtCQUFrQixFQUFFLE1BQU0sQ0FBQyxJQUFJLEVBQUUsTUFBTSxFQUFFLEtBQUssQ0FBQyxFQUFFLEVBQUUsU0FBUyxDQUFDLElBQUksRUFBRSxTQUFTLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxVQUFVO0VHWTNKLFdBQVcsRUFBRSxNQUFNO0VBQ25CLE9BQU8sRUFBRSxDQUFDO0VBQ1YsTUFBTSxFQUFFLENBQUM7RUFDVCxvQkFBb0IsRUFBRSxPQUFPO0VBQzdCLFdBQVcsRUFBRSxHQUFHO0VBQ2hCLFdBQVcsRUFBRSxJQUFJO0NBQ2pCOztBQUVELEFBQUEsRUFBRTtBQUNGLEVBQUU7QUFDRixFQUFFO0FBQ0YsRUFBRTtBQUNGLEVBQUU7QUFDRixFQUFFLENBQUM7RUFDRixLQUFLLEVBQUUsT0FBTztFQUNkLFNBQVMsRUFBRSxNQUFNO0VBQ2pCLFdBQVcsRUg1QkMsYUFBYSxFQUFFLGtCQUFrQixFQUFFLE1BQU0sQ0FBQyxJQUFJLEVBQUUsTUFBTSxFQUFFLEtBQUssQ0FBQyxFQUFFLEVBQUUsU0FBUyxDQUFDLElBQUksRUFBRSxTQUFTLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxVQUFVO0VHNkIzSixXQUFXLEVBQUUsSUFBSTtFQUNqQixNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0hoQ0csR0FBRyxDR2dDUyxDQUFDO0VBQzNCLG9CQUFvQixFQUFFLE9BQU87RUFDN0IsV0FBVyxFQUFFLEdBQUc7RUFDaEIsV0FBVyxFQUFFLElBQUk7Q0FLakI7O0FBbEJELEFBZUMsRUFmQyxBQWVBLE9BQU87QUFkVCxFQUFFLEFBY0EsT0FBTztBQWJULEVBQUUsQUFhQSxPQUFPO0FBWlQsRUFBRSxBQVlBLE9BQU87QUFYVCxFQUFFLEFBV0EsT0FBTztBQVZULEVBQUUsQUFVQSxPQUFPLENBQUM7RUFDUixXQUFXLEVBQUUsTUFBTTtDQUNuQjs7QUFHRixBQUFBLEVBQUUsQ0FBQztFQUNGLFNBQVMsRUFBRSxJQUFJO0NBQ2Y7O0FBRUQsQUFBQSxFQUFFLENBQUM7RUFDRixTQUFTLEVBQUUsSUFBSTtDQUNmOztBQUVELEFBQUEsRUFBRSxDQUFDO0VBQ0YsU0FBUyxFQUFFLElBQUk7Q0FDZjs7QUFFRCxBQUFBLEVBQUUsQ0FBQztFQUNGLFNBQVMsRUFBRSxJQUFJO0NBQ2Y7O0FBRUQsQUFBQSxFQUFFLENBQUM7RUFDRixTQUFTLEVBQUUsSUFBSTtDQUNmOztBQUVELEFBQUEsRUFBRSxDQUFDO0VBQ0YsU0FBUyxFQUFFLElBQUk7Q0FDZjs7QUFFRCxBQUFBLElBQUk7QUFDSixLQUFLLEFBQUEsS0FBSztBQUNWLENBQUM7QUFDRCxFQUFFO0FBQ0YsRUFBRSxDQUFDO0VBQ0YsU0FBUyxFSHRFQyxJQUFJO0VHdUVkLG9CQUFvQixFQUFFLE9BQU87RUFDN0IsV0FBVyxFQUFFLEdBQUc7RUFDaEIsV0FBVyxFQUFFLElBQUk7Q0FDakI7O0FBRUQsQUFBQSxDQUFDLENBQUM7RUFDRCxNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0g5RUcsR0FBRyxDRzhFUyxDQUFDO0VBRTNCLGFBQWEsRUFBRSxVQUFVO0VBQ3pCLFNBQVMsRUFBRSxVQUFVO0VBRXJCLGNBQWMsRUFBRSxTQUFTO0VBQ3pCLFVBQVUsRUFBRSxTQUFTO0VBRXJCLFdBQVcsRUFBRSxJQUFJO0VBQ2pCLFlBQVksRUFBRSxJQUFJO0VBQ2xCLGVBQWUsRUFBRSxJQUFJO0VBQ3JCLE9BQU8sRUFBRSxJQUFJO0NBZWI7O0FBM0JELEFBY0MsQ0FkQSxBQWNDLE1BQU0sRUFkUixDQUFDLEFBZUMsV0FBVyxDQUFDO0VBQ1osU0FBUyxFQUFFLElBQUk7Q0FDZjs7QUFqQkYsQUFtQkMsQ0FuQkEsQUFtQkMsS0FBSyxFQW5CUCxDQUFDLEFBb0JDLFVBQVUsQ0FBQztFQUNYLFdBQVcsRUFBRSxHQUFHO0NBQ2hCOztBQXRCRixBQXdCQyxDQXhCQSxDQXdCQSxDQUFDLENBQUM7RUFDRCxNQUFNLEVBQUUsT0FBTztDQUNmOztBQUdGLEFBQUEsS0FBSyxDQUFDO0VBQ0wsU0FBUyxFQUFFLEdBQUc7Q0FDZDs7QUFFRCxBQUFBLE1BQU0sQ0FBQztFQUNOLEtBQUssRUFBRSxJQUFJO0NBQ1g7O0FBRUQsQUFBQSxDQUFDLENBQUM7RUFDRCxLQUFLLEVGL0dNLE9BQU87Q0V5SGxCOztBQVhELEFBR0MsQ0FIQSxBQUdDLFFBQVEsQ0FBQztFQUNULEtBQUssRUZsSEssT0FBTztDRW1IakI7O0FBTEYsQUFPQyxDQVBBLEFBT0MsTUFBTSxFQVBSLENBQUMsQUFRQyxPQUFPLENBQUM7RUFDUixLQUFLLEVGdEhVLE9BQXlCO0NFdUh4Qzs7QUFHRixBQUFBLEVBQUUsQ0FBQyxDQUFDO0FBQ0osRUFBRSxDQUFDLENBQUMsQUFBQSxRQUFRO0FBQ1osRUFBRSxDQUFDLENBQUM7QUFDSixFQUFFLENBQUMsQ0FBQyxBQUFBLFFBQVE7QUFDWixFQUFFLENBQUMsQ0FBQztBQUNKLEVBQUUsQ0FBQyxDQUFDLEFBQUEsUUFBUTtBQUNaLEVBQUUsQ0FBQyxDQUFDO0FBQ0osRUFBRSxDQUFDLENBQUMsQUFBQSxRQUFRO0FBQ1osRUFBRSxDQUFDLENBQUM7QUFDSixFQUFFLENBQUMsQ0FBQyxBQUFBLFFBQVE7QUFDWixFQUFFLENBQUMsQ0FBQztBQUNKLEVBQUUsQ0FBQyxDQUFDLEFBQUEsUUFBUSxDQUFDO0VBQ1osS0FBSyxFRnZJTSxPQUFPO0NFd0lsQjs7QUFFRCxBQUFBLEtBQUssQUFBQSxZQUFZO0FBQ2pCLEVBQUUsQUFBQSxZQUFZO0FBQ2QsRUFBRSxBQUFBLFlBQVk7QUFDZCxFQUFFLEFBQUEsWUFBWTtBQUNkLEVBQUUsQUFBQSxZQUFZO0FBQ2QsRUFBRSxBQUFBLFlBQVk7QUFDZCxFQUFFLEFBQUEsWUFBWTtBQUNkLEVBQUUsQUFBQSxZQUFZO0FBQ2QsRUFBRSxBQUFBLFlBQVk7QUFDZCxDQUFDLEFBQUEsWUFBWTtBQUNiLElBQUksQUFBQSxZQUFZLENBQUM7RUFDaEIsVUFBVSxFQUFFLE1BQU07Q0FDbEI7O0FBRUQsQUFBQSxLQUFLLEFBQUEsVUFBVTtBQUNmLEVBQUUsQUFBQSxVQUFVO0FBQ1osRUFBRSxBQUFBLFVBQVU7QUFDWixFQUFFLEFBQUEsVUFBVTtBQUNaLEVBQUUsQUFBQSxVQUFVO0FBQ1osRUFBRSxBQUFBLFVBQVU7QUFDWixFQUFFLEFBQUEsVUFBVTtBQUNaLEVBQUUsQUFBQSxVQUFVO0FBQ1osRUFBRSxBQUFBLFVBQVU7QUFDWixDQUFDLEFBQUEsVUFBVTtBQUNYLElBQUksQUFBQSxVQUFVLENBQUM7RUFDZCxVQUFVLEVBQUUsSUFBSTtDQUNoQjs7QUFFRCxBQUFBLEtBQUssQUFBQSxXQUFXO0FBQ2hCLEVBQUUsQUFBQSxXQUFXO0FBQ2IsRUFBRSxBQUFBLFdBQVc7QUFDYixFQUFFLEFBQUEsV0FBVztBQUNiLEVBQUUsQUFBQSxXQUFXO0FBQ2IsRUFBRSxBQUFBLFdBQVc7QUFDYixFQUFFLEFBQUEsV0FBVztBQUNiLEVBQUUsQUFBQSxXQUFXO0FBQ2IsRUFBRSxBQUFBLFdBQVc7QUFDYixDQUFDLEFBQUEsV0FBVztBQUNaLElBQUksQUFBQSxXQUFXLENBQUM7RUFDZixVQUFVLEVBQUUsS0FBSztDQUNqQjs7QUFFRCxBQUFBLEtBQUssQUFBQSxRQUFRO0FBQ2IsRUFBRSxBQUFBLFFBQVE7QUFDVixFQUFFLEFBQUEsUUFBUTtBQUNWLEVBQUUsQUFBQSxRQUFRO0FBQ1YsRUFBRSxBQUFBLFFBQVE7QUFDVixFQUFFLEFBQUEsUUFBUTtBQUNWLEVBQUUsQUFBQSxRQUFRO0FBQ1YsRUFBRSxBQUFBLFFBQVE7QUFDVixFQUFFLEFBQUEsUUFBUTtBQUNWLENBQUMsQUFBQSxRQUFRO0FBQ1QsSUFBSSxBQUFBLFFBQVEsQ0FBQztFQUNaLEtBQUssRUZuTUksT0FBTztDRW9NaEI7O0FBRUQsQUFBQSxLQUFLLEFBQUEsT0FBTztBQUNaLEVBQUUsQUFBQSxPQUFPO0FBQ1QsRUFBRSxBQUFBLE9BQU87QUFDVCxFQUFFLEFBQUEsT0FBTztBQUNULEVBQUUsQUFBQSxPQUFPO0FBQ1QsRUFBRSxBQUFBLE9BQU87QUFDVCxFQUFFLEFBQUEsT0FBTztBQUNULEVBQUUsQUFBQSxPQUFPO0FBQ1QsRUFBRSxBQUFBLE9BQU87QUFDVCxDQUFDLEFBQUEsT0FBTztBQUNSLElBQUksQUFBQSxPQUFPLENBQUM7RUFDWCxLQUFLLEVGaE5HLE9BQU87Q0VpTmY7O0FBRUQsQUFBQSxLQUFLLEFBQUEsS0FBSztBQUNWLEVBQUUsQUFBQSxLQUFLO0FBQ1AsRUFBRSxBQUFBLEtBQUs7QUFDUCxFQUFFLEFBQUEsS0FBSztBQUNQLEVBQUUsQUFBQSxLQUFLO0FBQ1AsRUFBRSxBQUFBLEtBQUs7QUFDUCxFQUFFLEFBQUEsS0FBSztBQUNQLEVBQUUsQUFBQSxLQUFLO0FBQ1AsRUFBRSxBQUFBLEtBQUs7QUFDUCxDQUFDLEFBQUEsS0FBSztBQUNOLElBQUksQUFBQSxLQUFLLENBQUM7RUFDVCxLQUFLLEVGN05DLE9BQU87Q0U4TmI7O0FBRUQsQUFBQSxJQUFJLEFBQUEsWUFBWSxDQUFDO0VBQ2hCLE9BQU8sRUFBRSxLQUFLO0VBQ2QsS0FBSyxFQUFFLElBQUk7RUFDWCxVQUFVLEVBQUUsTUFBTTtDQUNsQjs7QUFFRCxBQUFBLEVBQUU7QUFDRixFQUFFLENBQUM7RUFDRixNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0gxT0csR0FBRyxDRzBPUyxJQUFJO0VBQzlCLE9BQU8sRUFBRSxDQUFDO0NBV1Y7O0FBZEQsQUFLQyxFQUxDLENBS0QsRUFBRTtBQUpILEVBQUUsQ0FJRCxFQUFFLENBQUM7RUFDRixlQUFlLEVBQUUsT0FBTztFQUN4QixXQUFXLEVBQUUsR0FBRztDQUNoQjs7QUFSRixBQVVDLEVBVkMsQ0FVRCxFQUFFO0FBVkgsRUFBRSxDQVdELEVBQUU7QUFWSCxFQUFFLENBU0QsRUFBRTtBQVRILEVBQUUsQ0FVRCxFQUFFLENBQUM7RUFDRixhQUFhLEVBQUUsWUFBWTtDQUMzQjs7QUFHRiwwQ0FBMEM7QUFDMUMsQUFBQSxVQUFVLENBQUM7RUFDVixVQUFVLEVBQUUsb0JBQW9CO0NBS2hDOztBQU5ELEFBR0MsVUFIUyxHQUdQLENBQUMsQ0FBQztFQUNILFVBQVUsRUFBRSxvQkFBb0I7Q0FDaEM7O0FGaFBGLFVBQVU7QUFDVixBQUFBLEtBQUs7QUFDTCxJQUFJLENBQUM7RUFDSixnQkFBZ0IsRUFYQyxPQUFPO0VBWXhCLFVBQVUsRUFBRSxNQUFNO0VBQ2xCLE9BQU8sRUFBRSxhQUFhO0NBQ3RCOztBQUVELEFBQUEsVUFBVSxDQUFDO0VBQ1YsTUFBTSxFQUFFLGFBQWE7Q0FDckI7O0FBRUQsQUFBQSxPQUFPLENBQUM7RUFDUCxXQUFXLEVBQUUsQ0FBQztDQWFkOztBQWRELEFBR0MsT0FITSxDQUdOLGFBQWEsQ0FBQztFQUNiLE9BQU8sRUFBRSxZQUFZO0VBQ3JCLGNBQWMsRUFBRSxNQUFNO0VBQ3RCLEtBQUssRUFBRSxHQUFHO0NBQ1Y7O0FBUEYsQUFTQyxPQVRNLENBU04sR0FBRyxDQUFDO0VBQ0gsT0FBTyxFQUFFLHVCQUF1QjtFQUNoQyxVQUFVLEVBQUUsS0FBSztFQUNqQixjQUFjLEVBQUUsTUFBTTtDQUN0Qjs7QUFJRixBQUNDLGVBRGMsQUFDYixVQUFVLENBQUM7RUFDWCxPQUFPLEVBQUUsSUFBSTtDQUNiOztBQUdGLEFBQUEsUUFBUSxDQUFDO0VBV1IsdUNBQXVDO0NBZ0J2Qzs7QUEzQkQsQUFFQyxRQUZPLENBRVAsQ0FBQyxFQUZGLFFBQVEsQ0FFSixDQUFDLEVBRkwsUUFBUSxDQUVELEdBQUcsQ0FBQztFQUNULGNBQWMsRUFBRSxVQUFVO0VBQzFCLFVBQVUsRUFBRSxVQUFVO0NBQ3RCOztBQUxGLEFBT0MsUUFQTyxDQU9QLEdBQUcsQ0FBQztFQUNILFdBQVcsRUFBRSxPQUFPO0NBQ3BCOztBQVRGLEFBWUMsUUFaTyxDQVlQLE9BQU8sQ0FBQztFQUNQLE9BQU8sRUFBRSxZQUFZO0NBQ3JCOztBQWRGLEFBaUJFLFFBakJNLENBZ0JQLFVBQVUsQ0FDVCxLQUFLLEFBQUEsSUFBSyxDQUFBLDhCQUE4QixFQUFFO0VBQ3pDLGVBQWUsRUFBRSxRQUFRO0VBQ3pCLEtBQUssRUFBRSxJQUFJO0NBTVg7O0FBekJILEFBcUJHLFFBckJLLENBZ0JQLFVBQVUsQ0FDVCxLQUFLLEFBQUEsSUFBSyxDQUFBLDhCQUE4QixFQUl2QyxFQUFFLEVBckJMLFFBQVEsQ0FnQlAsVUFBVSxDQUNULEtBQUssQUFBQSxJQUFLLENBQUEsOEJBQThCLEVBSW5DLEVBQUUsQ0FBQztFQUNOLE1BQU0sRUFBRSxzQkFBc0I7RUFDOUIsT0FBTyxFQUFFLGNBQWM7Q0FDdkI7O0FBS0osQUFBQSxRQUFRLENBQUMsRUFBRSxHQUFHLENBQUMsQUFBQSxXQUFXLENBQUM7RUFDMUIsYUFBYSxFQUFFLENBQUM7Q0FDaEI7O0FBRUQsQUFBQSxPQUFPLENBQUM7RUFDUCxLQUFLLEVBQUUsT0FBd0I7Q0FVL0I7O0FBWEQsQUFHQyxPQUhNLENBR04sQ0FBQyxDQUFDO0VBQ0QsS0FBSyxFQUFFLE9BQXdCO0VBQy9CLGVBQWUsRUFBRSxTQUFTO0NBSzFCOztBQVZGLEFBT0UsT0FQSyxDQUdOLENBQUMsQUFJQyxNQUFNLENBQUM7RUFDUCxLQUFLLEVBbEZJLE9BQU87Q0FtRmhCOztBQUlILGdCQUFnQjtBQUNoQixBQUFBLEVBQUU7QUFDRixFQUFFO0FBQ0YsRUFBRTtBQUNGLEVBQUU7QUFDRixFQUFFO0FBQ0YsRUFBRSxDQUFDO0VBQ0YsS0FBSyxFQTlGTSxPQUFPO0NBK0ZsQjs7QUFFRCxhQUFhO0FBQ2IsQUFBQSxZQUFZLENBQUM7RUFDWixhQUFhLEVBQUUsR0FBRztFQUNsQixPQUFPLEVBQUUsUUFBUTtFQUNqQixlQUFlLEVBQUUsSUFBSTtDQUNyQjs7QUFFRCxhQUFhO0FBSVosQUFDQyxnQkFEZSxDQUNmLEVBQUUsQUFBQSxZQUFZLENBQUM7RUFDZCxXQUFXLEVBQUUsQ0FBQztDQUNkOztBQUhGLEFBQ0MsYUFEWSxDQUNaLEVBQUUsQUFBQSxZQUFZLENBQUM7RUFDZCxXQUFXLEVBQUUsQ0FBQztDQUNkOztBQUlILEFBQ0MsRUFEQyxBQUFBLElBQUssQ0FBQSxVQUFVLElBQ2QsWUFBWSxDQUFDLElBQUksQ0FBQztFQUNuQixPQUFPLEVBQUUsS0FBSztDQUNkOztBQUdGLHVCQUF1QjtBQUN2QixBQUFBLG9CQUFvQjtBQUNwQixrQkFBa0IsQ0FBQztFQUNsQixTQUFTLEVBQUUsSUFBSTtDQUNmOztBQUVELGlCQUFpQjtBQUNqQixBQUNDLGtCQURpQixDQUNqQixZQUFZLENBQUMsVUFBVSxDQUFDO0VBQ3ZCLE9BQU8sRUFBRSxZQUFZO0VBQ3JCLGNBQWMsRUFBRSxNQUFNO0NBQ3RCOztBQUdGLHVCQUF1QjtBQUN2QixBQUNDLGVBRGMsQ0FDZCxZQUFZLEFBQUEsV0FBVyxFQURQLGNBQWMsQ0FDOUIsWUFBWSxBQUFBLFdBQVcsQ0FBQztFQUN2QixPQUFPLEVBQUUsWUFBWTtFQUNyQixLQUFLLEVBQUUsSUFBSTtDQUNYOztBQUpGLEFBTUMsZUFOYyxDQU1kLENBQUMsQ0FBQyxVQUFVO0FBTmIsZUFBZSxDQU9kLEVBQUUsQ0FBQyxVQUFVLEVBUEcsY0FBYyxDQU05QixDQUFDLENBQUMsVUFBVTtBQU5JLGNBQWMsQ0FPOUIsRUFBRSxDQUFDLFVBQVUsQ0FBQztFQUNiLEtBQUssRUFBRSxJQUFJO0VBQ1gsWUFBWSxFQUFFLElBQUk7RUFDbEIsVUFBVSxFQUFFLEdBQUc7RUFDZixhQUFhLEVBQUUsR0FBRztDQUNsQjs7QUFaRixBQWNDLGVBZGMsQ0FjZCxDQUFDLENBQUMsWUFBWTtBQWRmLGVBQWUsQ0FlZCxFQUFFLENBQUMsWUFBWSxFQWZDLGNBQWMsQ0FjOUIsQ0FBQyxDQUFDLFlBQVk7QUFkRSxjQUFjLENBZTlCLEVBQUUsQ0FBQyxZQUFZLENBQUM7RUFDZixPQUFPLEVBQUUsS0FBSztFQUNkLFdBQVcsRUFBRSxJQUFJO0VBQ2pCLFlBQVksRUFBRSxJQUFJO0NBQ2xCOztBQW5CRixBQXFCQyxlQXJCYyxDQXFCZCxDQUFDLENBQUMsV0FBVztBQXJCZCxlQUFlLENBc0JkLEVBQUUsQ0FBQyxXQUFXLEVBdEJFLGNBQWMsQ0FxQjlCLENBQUMsQ0FBQyxXQUFXO0FBckJHLGNBQWMsQ0FzQjlCLEVBQUUsQ0FBQyxXQUFXLENBQUM7RUFDZCxLQUFLLEVBQUUsS0FBSztFQUNaLFdBQVcsRUFBRSxJQUFJO0VBQ2pCLFVBQVUsRUFBRSxHQUFHO0VBQ2YsYUFBYSxFQUFFLEdBQUc7Q0FDbEI7O0FBM0JGLEFBNkJDLGVBN0JjLENBNkJkLEtBQUssRUE3QlcsY0FBYyxDQTZCOUIsS0FBSyxDQUFDO0VBQ0wsZUFBZSxFQUFFLFFBQVE7RUFDekIsS0FBSyxFQUFFLElBQUk7Q0FNWDs7QUFyQ0YsQUFpQ0UsZUFqQ2EsQ0E2QmQsS0FBSyxDQUlKLEVBQUUsRUFqQ0osZUFBZSxDQTZCZCxLQUFLLENBSUEsRUFBRSxFQWpDUyxjQUFjLENBNkI5QixLQUFLLENBSUosRUFBRSxFQWpDYSxjQUFjLENBNkI5QixLQUFLLENBSUEsRUFBRSxDQUFDO0VBQ04sTUFBTSxFQUFFLHNCQUFzQjtFQUM5QixPQUFPLEVBQUUsY0FBYztDQUN2Qjs7QUFJSCxBQUNDLGFBRFksQ0FDWixZQUFZLENBQUM7RUFDWixXQUFXLEVBQUUsY0FBYztDQUMzQjs7QUFLRixBR25MQyxvQkhtTG1CLENHbkxuQixnQ0FBZ0M7QUhvTGpDLFVBQVUsQ0dwTFQsZ0NBQWdDLENBQUM7RUFNaEMsT0FBTyxFQUFFLEtBQUs7RUFDZCxTQUFTLEVBWFMsR0FBRztDQWlGckI7O0FIc0dGLEFHbExFLG9CSGtMa0IsQ0duTG5CLGdDQUFnQyxDQUMvQixDQUFDO0FIbUxILFVBQVUsQ0dwTFQsZ0NBQWdDLENBQy9CLENBQUMsQ0FBQTtFQUNBLFVBQVUsRUFBRSxVQUFVO0VBQ3RCLFVBQVUsRUFBRSxVQUFVO0NBQ3RCOztBSCtLSCxBRzFLRSxvQkgwS2tCLENHbkxuQixnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QjtBSDJLckMsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUFDO0VBQ25DLEtBQUssRUFBRSxJQUFJO0VBQ1gsWUFBWSxFQUFFLEtBQUs7Q0FpRW5COztBSHVHSCxBR3JLSSxvQkhxS2dCLENHbkxuQixnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxFSHFLTixvQkFBb0IsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFO0FIc0tWLFVBQVUsQ0dwTFQsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNELEVBQUU7QUhzS04sVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0csRUFBRSxDQUFDO0VBQ04sVUFBVSxFQUFFLE1BQU07Q0FrRGxCOztBSGtITCxBR2pLSyxvQkhpS2UsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRCxFQUFFLEFBSUEsaUNBQWlDLEVIaUt2QyxvQkFBb0IsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFLEFBSUosaUNBQWlDO0FIa0t2QyxVQUFVLENHcExULGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRCxFQUFFLEFBSUEsaUNBQWlDO0FIa0t2QyxVQUFVLENHcExULGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFLEFBSUosaUNBQWlDLENBQUM7RUFDbEMsVUFBVSxFQUFFLElBQUk7Q0FLaEI7O0FBSEEsQUFBQSxJQUFJLENIOEpWLG9CQUFvQixDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNELEVBQUUsQUFJQSxpQ0FBaUMsRUFHakMsSUFBSSxDSDhKVixvQkFBb0IsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFLEFBSUosaUNBQWlDLEVBR2pDLElBQUk7QUgrSlYsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxBQUlBLGlDQUFpQyxFQUdqQyxJQUFJO0FIK0pWLFVBQVUsQ0dwTFQsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUFJSixpQ0FBaUMsQ0FHMUI7RUFDTixVQUFVLEVBQUUsS0FBSztDQUNqQjs7QUg0SlAsQUdwSkssb0JIb0plLENHbkxuQixnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxBQWlCQSxvQ0FBb0MsRUhvSjFDLG9CQUFvQixDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUFpQkosb0NBQW9DO0FIcUoxQyxVQUFVLENHcExULGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRCxFQUFFLEFBaUJBLG9DQUFvQztBSHFKMUMsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0csRUFBRSxBQWlCSixvQ0FBb0MsQ0FBQztFQUNyQyxLQUFLLEVBQUUsR0FBRztDQW9CVjs7QUgrSE4sQUdqSk0sb0JIaUpjLENHbkxuQixnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxBQWlCQSxvQ0FBb0MsQ0FHcEMsZ0RBQWdELEVIaUp0RCxvQkFBb0IsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFLEFBaUJKLG9DQUFvQyxDQUdwQyxnREFBZ0Q7QUhrSnRELFVBQVUsQ0dwTFQsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNELEVBQUUsQUFpQkEsb0NBQW9DLENBR3BDLGdEQUFnRDtBSGtKdEQsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0csRUFBRSxBQWlCSixvQ0FBb0MsQ0FHcEMsZ0RBQWdELENBQUM7RUFDaEQsT0FBTyxFQUFFLElBQUk7Q0FDYjs7QUFFRCxNQUFNLEVBQUUsU0FBUyxFQUFFLEtBQUs7RUg2STlCLEFHcEpLLG9CSG9KZSxDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNELEVBQUUsQUFpQkEsb0NBQW9DLEVIb0oxQyxvQkFBb0IsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFLEFBaUJKLG9DQUFvQztFSHFKMUMsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxBQWlCQSxvQ0FBb0M7RUhxSjFDLFVBQVUsQ0dwTFQsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUFpQkosb0NBQW9DLENBQUM7SUFXcEMsS0FBSyxFQUFFLEdBQUc7R0FVWDtFSCtITixBR3ZJTyxvQkh1SWEsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRCxFQUFFLEFBaUJBLG9DQUFvQyxDQWFuQyxnREFBZ0QsRUh1SXZELG9CQUFvQixDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUFpQkosb0NBQW9DLENBYW5DLGdEQUFnRDtFSHdJdkQsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxBQWlCQSxvQ0FBb0MsQ0FhbkMsZ0RBQWdEO0VId0l2RCxVQUFVLENHcExULGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFLEFBaUJKLG9DQUFvQyxDQWFuQyxnREFBZ0QsQ0FBQztJQUNoRCxPQUFPLEVBQUUsTUFBTTtHQUNmO0VIcUlSLEFHbklPLG9CSG1JYSxDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNELEVBQUUsQUFpQkEsb0NBQW9DLENBaUJuQywrQ0FBK0MsRUhtSXRELG9CQUFvQixDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUFpQkosb0NBQW9DLENBaUJuQywrQ0FBK0M7RUhvSXRELFVBQVUsQ0dwTFQsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNELEVBQUUsQUFpQkEsb0NBQW9DLENBaUJuQywrQ0FBK0M7RUhvSXRELFVBQVUsQ0dwTFQsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUFpQkosb0NBQW9DLENBaUJuQywrQ0FBK0MsQ0FBQztJQUMvQyxPQUFPLEVBQUUsSUFBSTtHQUNiOzs7QUhpSVIsQUcxSEssb0JIMEhlLENHbkxuQixnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxBQTJDQSxpQ0FBaUMsRUgwSHZDLG9CQUFvQixDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUEyQ0osaUNBQWlDO0FIMkh2QyxVQUFVLENHcExULGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRCxFQUFFLEFBMkNBLGlDQUFpQztBSDJIdkMsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0csRUFBRSxBQTJDSixpQ0FBaUMsQ0FBQztFQUNsQyxLQUFLLEVBQUUsR0FBRztFQUNWLFVBQVUsRUFBRSxLQUFLO0NBS2pCOztBQUhBLEFBQUEsSUFBSSxDSHNIVixvQkFBb0IsQ0duTG5CLGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRCxFQUFFLEFBMkNBLGlDQUFpQyxFQUlqQyxJQUFJLENIc0hWLG9CQUFvQixDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxDQUNHLEVBQUUsQUEyQ0osaUNBQWlDLEVBSWpDLElBQUk7QUh1SFYsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLENBQ0QsRUFBRSxBQTJDQSxpQ0FBaUMsRUFJakMsSUFBSTtBSHVIVixVQUFVLENHcExULGdDQUFnQyxDQVMvQixLQUFLLEFBQUEsOEJBQThCLENBSWxDLEVBQUUsQ0FDRyxFQUFFLEFBMkNKLGlDQUFpQyxDQUkxQjtFQUNOLFVBQVUsRUFBRSxJQUFJO0NBQ2hCOztBSG9IUCxBRy9HSSxvQkgrR2dCLENHbkxuQixnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLEFBdURBLGtDQUFrQyxDQUFDLEVBQUU7QUhnSDFDLFVBQVUsQ0dwTFQsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxBQXVEQSxrQ0FBa0MsQ0FBQyxFQUFFLENBQUM7RUFDdEMsVUFBVSxFQUFFLElBQUk7Q0FLaEI7O0FBSEEsQUFBQSxJQUFJLENINEdULG9CQUFvQixDR25MbkIsZ0NBQWdDLENBUy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FJbEMsRUFBRSxBQXVEQSxrQ0FBa0MsQ0FBQyxFQUFFLEVBR3JDLElBQUk7QUg2R1QsVUFBVSxDR3BMVCxnQ0FBZ0MsQ0FTL0IsS0FBSyxBQUFBLDhCQUE4QixDQUlsQyxFQUFFLEFBdURBLGtDQUFrQyxDQUFDLEVBQUUsQ0FHOUI7RUFDTixVQUFVLEVBQUUsS0FBSztDQUNqQjs7QUgwR04sQUdqRkMsb0JIaUZtQixDR2pGbkIsS0FBSyxBQUFBLDhCQUE4QjtBSGtGcEMsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBQUM7RUFDbkMsYUFBYSxFQUFFLEdBQUc7RUFDbEIsTUFBTSxFQUFFLGlCQUFpQjtFQUN6QixlQUFlLEVBQUUsUUFBUTtDQTJEekI7O0FIbUJGLEFHMUVHLG9CSDBFaUIsQ0dqRm5CLEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxDQUVELEVBQUU7QUgyRUwsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FFRCxFQUFFLENBQUM7RUFDRixVQUFVLEVBQUUsaUJBQWlCO0VBQzdCLGFBQWEsRUFBRSxJQUFJO0VBQ25CLFdBQVcsRUFBRSxJQUFJO0VBQ2pCLFlBQVksRUFBRSxJQUFJO0NBQ2xCOztBSHFFSixBR25FRyxvQkhtRWlCLENHakZuQixLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FTRCxFQUFFO0FIb0VMLFVBQVUsQ0dsRlQsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBU0QsRUFBRSxDQUFDO0VBQ0YsV0FBVyxFQUFFLEdBQUc7RUFDaEIsTUFBTSxFQUFFLElBQUk7Q0FDWjs7QUhnRUosQUc5REcsb0JIOERpQixDR2pGbkIsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0QsRUFBRSxFSDhETCxvQkFBb0IsQ0dqRm5CLEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxDQWNHLEVBQUU7QUgrRFQsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRCxFQUFFO0FIK0RMLFVBQVUsQ0dsRlQsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0csRUFBRSxDQUFDO0VBQ04sT0FBTyxFQUFFLEtBQUs7RUFDZCxXQUFXLEVBQUUsSUFBSTtFQUNqQixVQUFVLEVBQUUsSUFBSTtDQXNCaEI7O0FIcUNKLEFHeERJLG9CSHdEZ0IsQ0dqRm5CLEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxDQWNELEVBQUUsQUFNQSxpQ0FBaUMsRUh3RHRDLG9CQUFvQixDR2pGbkIsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0csRUFBRSxBQU1KLGlDQUFpQztBSHlEdEMsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRCxFQUFFLEFBTUEsaUNBQWlDO0FIeUR0QyxVQUFVLENHbEZULEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxDQWNHLEVBQUUsQUFNSixpQ0FBaUMsQ0FBQztFQUNsQyxTQUFTLEVBQUUsT0FBTztFQUNsQixZQUFZLEVBQUUsSUFBSTtDQU1sQjs7QUFKQSxBQUFBLElBQUksQ0hvRFQsb0JBQW9CLENHakZuQixLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRCxFQUFFLEFBTUEsaUNBQWlDLEVBSWpDLElBQUksQ0hvRFQsb0JBQW9CLENHakZuQixLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRyxFQUFFLEFBTUosaUNBQWlDLEVBSWpDLElBQUk7QUhxRFQsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRCxFQUFFLEFBTUEsaUNBQWlDLEVBSWpDLElBQUk7QUhxRFQsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRyxFQUFFLEFBTUosaUNBQWlDLENBSTFCO0VBQ04sYUFBYSxFQUFFLElBQUk7RUFDbkIsWUFBWSxFQUFFLENBQUM7Q0FDZjs7QUhpRE4sQUc3Q0ksb0JINkNnQixDR2pGbkIsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0QsRUFBRSxBQWlCQSxpQ0FBaUMsRUg2Q3RDLG9CQUFvQixDR2pGbkIsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0csRUFBRSxBQWlCSixpQ0FBaUM7QUg4Q3RDLFVBQVUsQ0dsRlQsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0QsRUFBRSxBQWlCQSxpQ0FBaUM7QUg4Q3RDLFVBQVUsQ0dsRlQsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0csRUFBRSxBQWlCSixpQ0FBaUMsQ0FBQztFQUNsQyxhQUFhLEVBQUUsSUFBSTtDQU1uQjs7QUFKQSxBQUFBLElBQUksQ0gwQ1Qsb0JBQW9CLENHakZuQixLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRCxFQUFFLEFBaUJBLGlDQUFpQyxFQUdqQyxJQUFJLENIMENULG9CQUFvQixDR2pGbkIsS0FBSyxBQUFBLDhCQUE4QixDQUtsQyxFQUFFLENBY0csRUFBRSxBQWlCSixpQ0FBaUMsRUFHakMsSUFBSTtBSDJDVCxVQUFVLENHbEZULEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxDQWNELEVBQUUsQUFpQkEsaUNBQWlDLEVBR2pDLElBQUk7QUgyQ1QsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQ0FjRyxFQUFFLEFBaUJKLGlDQUFpQyxDQUcxQjtFQUNOLFlBQVksRUFBRSxJQUFJO0VBQ2xCLGFBQWEsRUFBRSxDQUFDO0NBQ2hCOztBSHVDTixBR2xDRyxvQkhrQ2lCLENHakZuQixLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQUEwQ0Esa0NBQWtDLENBQUMsRUFBRTtBSG1DekMsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQUEwQ0Esa0NBQWtDLENBQUMsRUFBRSxDQUFDO0VBQ3RDLFlBQVksRUFBRSxJQUFJO0NBTWxCOztBQUpBLEFBQUEsSUFBSSxDSCtCUixvQkFBb0IsQ0dqRm5CLEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxBQTBDQSxrQ0FBa0MsQ0FBQyxFQUFFLEVBR3JDLElBQUk7QUhnQ1IsVUFBVSxDR2xGVCxLQUFLLEFBQUEsOEJBQThCLENBS2xDLEVBQUUsQUEwQ0Esa0NBQWtDLENBQUMsRUFBRSxDQUc5QjtFQUNOLGFBQWEsRUFBRSxJQUFJO0VBQ25CLFlBQVksRUFBRSxDQUFDO0NBQ2Y7O0FINEJMLEFHeEJHLG9CSHdCaUIsQ0dqRm5CLEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxBQW9EQSx1Q0FBdUMsQ0FBQyxFQUFFO0FId0I5QyxvQkFBb0IsQ0dqRm5CLEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxBQXFEQSxvQ0FBb0MsQ0FBQyxFQUFFO0FId0IzQyxVQUFVLENHbEZULEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxBQW9EQSx1Q0FBdUMsQ0FBQyxFQUFFO0FIeUI5QyxVQUFVLENHbEZULEtBQUssQUFBQSw4QkFBOEIsQ0FLbEMsRUFBRSxBQXFEQSxvQ0FBb0MsQ0FBQyxFQUFFLENBQUM7RUFDeEMsV0FBVyxFQUFFLEdBQUc7Q0FDaEI7O0FIcUJKLEFBS0Msb0JBTG1CLENBS25CLGdDQUFnQztBQUpqQyxVQUFVLENBSVQsZ0NBQWdDLENBQUM7RUFDaEMsU0FBUyxFQUFFLElBQUk7Q0FlZjs7QUFyQkYsQUFTRyxvQkFUaUIsQ0FLbkIsZ0NBQWdDLENBRy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FDbEMsT0FBTztBQVRWLG9CQUFvQixDQUtuQixnQ0FBZ0MsQ0FHL0IsS0FBSyxBQUFBLDhCQUE4QixDQUVsQyx5Q0FBeUM7QUFWNUMsb0JBQW9CLENBS25CLGdDQUFnQyxDQUcvQixLQUFLLEFBQUEsOEJBQThCLENBR2xDLGdEQUFnRDtBQVZuRCxVQUFVLENBSVQsZ0NBQWdDLENBRy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FDbEMsT0FBTztBQVJWLFVBQVUsQ0FJVCxnQ0FBZ0MsQ0FHL0IsS0FBSyxBQUFBLDhCQUE4QixDQUVsQyx5Q0FBeUM7QUFUNUMsVUFBVSxDQUlULGdDQUFnQyxDQUcvQixLQUFLLEFBQUEsOEJBQThCLENBR2xDLGdEQUFnRCxDQUFDO0VBQ2hELE9BQU8sRUFBRSxJQUFJO0NBQ2I7O0FBYkosQUFnQkksb0JBaEJnQixDQUtuQixnQ0FBZ0MsQ0FHL0IsS0FBSyxBQUFBLDhCQUE4QixDQU9sQyxFQUFFLEFBQ0EsMkNBQTJDLENBQUMsRUFBRSxBQUFBLGlDQUFpQztBQWZwRixVQUFVLENBSVQsZ0NBQWdDLENBRy9CLEtBQUssQUFBQSw4QkFBOEIsQ0FPbEMsRUFBRSxBQUNBLDJDQUEyQyxDQUFDLEVBQUUsQUFBQSxpQ0FBaUMsQ0FBQztFQUNoRixLQUFLLEVBeE1GLE9BQU8sQ0F3TUksVUFBVTtDQUN4Qjs7QUlsTkwsVUFBVTtBQUNWLEFBQUEsV0FBVyxDQUFDO0VBQ1gsV0FBVyxFQUFFLElBQUk7RUFDakIsY0FBYyxFQUFFLElBQUk7Q0FDcEI7O0FBRUQsQUFBQSxRQUFRLENBQUM7RUFDUixTQUFTLEVBQUUsS0FBSztDQUNoQjs7QUFFRCxBQUFBLGNBQWMsQ0FBQztFQUNkLGdCQUFnQixFSkpHLE9BQU87RUlLMUIsTUFBTSxFQUFFLEdBQUcsQ0FBQyxLQUFLLENBQUMsT0FBd0I7RUFDMUMsT0FBTyxFQUFFLG1CQUFtQjtDQUM1Qjs7QUFFRCxBQUFBLE9BQU8sQ0FBQztFQUNQLFVBQVUsRUFBRSxNQUFNO0VBQ2xCLE9BQU8sRUFBRSxVQUFVO0NBTW5COztBQVJELEFBSUMsT0FKTSxDQUlOLGFBQWEsQ0FBQztFQUNiLG1EQUFtRDtFQUNuRCxNQUFNLEVBQUUsYUFBYTtDQUNyQjs7QUFHRixBQUFBLE9BQU8sQ0FBQztFQUNQLFNBQVMsRUFBRSxJQUFJO0VBQ2YsV0FBVyxFQUFFLElBQUk7RUFDakIsVUFBVSxFQUFFLE1BQU07RUFDbEIsV0FBVyxFQUFFLElBQUk7Q0FDakI7O0FBRUQsZ0JBQWdCO0FBQ2hCLEFBQUEsSUFBSTtBQUNKLEtBQUssQUFBQSxLQUFLO0FBQ1YsRUFBRTtBQUNGLEVBQUU7QUFDRixFQUFFO0FBQ0YsRUFBRTtBQUNGLEVBQUU7QUFDRixFQUFFO0FBQ0YsQ0FBQztBQUNELEVBQUUsQUFBQSxJQUFLLENBQUEsT0FBTztBQUNkLEVBQUU7QUFDRixDQUFDLENBQUM7RUFDRCxXQUFXLEVBQUUsSUFBSTtDQUNqQjs7QUFFRCxhQUFhO0FBQ2IsQUFDQyxRQURPLENBQ1AsV0FBVyxDQUFDO0VBQ1gsV0FBVyxFQUFFLElBQUk7RUFDakIsY0FBYyxFTHJERCxHQUFHO0NLc0RoQjs7QUFKRixBQU1DLFFBTk8sQ0FNUCxZQUFZLENBQUM7RUFDWixjQUFjLEVBQUUsSUFBSTtFQUNwQixhQUFhLEVBQUUsR0FBRyxDQUFDLEtBQUssQ0FBQyxPQUF3QjtDQUNqRDs7QUFURixBQVdDLFFBWE8sQ0FXUCxXQUFXLEFBQUEsWUFBWSxDQUFDO0VBQ3ZCLFdBQVcsRUFBRSxJQUFJO0NBQ2pCOztBQUlGLEFBQUEsb0JBQW9CLENBQUMsZ0NBQWdDLENBQUMsS0FBSyxBQUFBLDhCQUE4QixDQUFDO0VBQ3pGLGFBQWEsRUFBRSxDQUFDO0NBQ2hCOztBQUVELEFBQ0MscUJBRG9CLEdBQ2xCLEVBQUUsQ0FBQztFQUNKLGFBQWEsRUFBRSxHQUFHLENBQUMsS0FBSyxDQUFDLE9BQXdCO0NBQ2pEOztBQUVBLEFBSUUsb0NBSmEsQ0FFZix5QkFBeUIsQ0FDeEIsRUFBRSxBQUNBLFlBQVksRUFIZixxQ0FBZ0IsQ0FDaEIseUJBQXlCLENBQ3hCLEVBQUUsQUFDQSxZQUFZLENBQUM7RUFDYixjQUFjLEVBQUUsQ0FBQztDQUNqQjs7QUNsRkwsQUFFQyxxQkFGb0IsR0FFbEIsRUFBRSxDQUFDO0VBQ0osY0FBYyxFQUFFLElBQUk7Q0FDcEI7O0FBSkYsQUFNQyxxQkFOb0IsQ0FNcEIseUJBQXlCLENBQUM7RUFDekIsS0FBSyxFQUFFLElBQUk7Q0FhWDs7QUFwQkYsQUFTRSxxQkFUbUIsQ0FNcEIseUJBQXlCLENBR3hCLFlBQVksQ0FBQztFQUNaLGNBQWMsRUFBRSxJQUFJO0NBQ3BCOztBQVhILEFBYUUscUJBYm1CLENBTXBCLHlCQUF5QixHQU90QixFQUFFLEdBQUcsRUFBRSxDQUFDO0VBQ1QsYUFBYSxFQUFFLElBQUk7Q0FLbkI7O0FBbkJILEFBZ0JHLHFCQWhCa0IsQ0FNcEIseUJBQXlCLEdBT3RCLEVBQUUsR0FBRyxFQUFFLEFBR1AsV0FBVyxDQUFDO0VBQ1osYUFBYSxFQUFFLENBQUM7Q0FDaEI7O0FBSUYsQUFHRSxrQ0FIVyxDQUNiLHlCQUF5QixBQUN2QixJQUFLLENBQUEsa0JBQWtCLEVBQ3ZCLEVBQUUsQUFBQSxXQUFXLENBQUM7RUFDYixPQUFPLEVBQUUsSUFBSTtDQUNiOztBQUxILEFBUUMsa0NBUlksQ0FDYix5QkFBeUIsQ0FPeEIsWUFBWSxDQUFDO0VBQ1osY0FBYyxFQUFFLElBQUk7Q0FDcEI7O0FBVkYsQUFhRSxrQ0FiVyxDQUNiLHlCQUF5QixBQVd2QixXQUFXLENBQ1gsWUFBWSxDQUFDO0VBQ1osY0FBYyxFQUFFLENBQUM7Q0FDakI7O0FBS0gsQUFLRyxvQ0FMWSxDQUVmLHlCQUF5QixDQUN4QixFQUFFLEFBQUEsV0FBVyxDQUNaLDBCQUEwQixDQUN6QixZQUFZLEVBSmYscUNBQWdCLENBQ2hCLHlCQUF5QixDQUN4QixFQUFFLEFBQUEsV0FBVyxDQUNaLDBCQUEwQixDQUN6QixZQUFZLENBQUM7RUFDWixjQUFjLEVBQUUsQ0FBQztDQUNqQjs7QUFqRE4sQUF3REUscUJBeERtQixDQXVEcEIsMEJBQTBCLENBQ3pCLEVBQUUsQ0FBQztFQUNGLE1BQU0sRUFBRSxZQUFZO0NBQ3BCOztBQTFESCxBQStESSxxQkEvRGlCLENBdURwQiwwQkFBMEIsQUFLeEIsaUJBQWlCLENBRWpCLG9CQUFvQixDQUNuQixzQkFBc0IsRUEvRDFCLHFCQUFxQixDQXVEcEIsMEJBQTBCLEFBTXhCLGlCQUFpQixDQUNqQixvQkFBb0IsQ0FDbkIsc0JBQXNCLENBQUM7RUFDdEIsT0FBTyxFQUFFLGdCQUFnQjtDQUN6Qjs7QUFqRUwsQUFtRUkscUJBbkVpQixDQXVEcEIsMEJBQTBCLEFBS3hCLGlCQUFpQixDQUVqQixvQkFBb0IsQ0FLbkIsOEJBQThCLEVBbkVsQyxxQkFBcUIsQ0F1RHBCLDBCQUEwQixBQU14QixpQkFBaUIsQ0FDakIsb0JBQW9CLENBS25CLDhCQUE4QixDQUFDO0VBQzlCLE9BQU8sRUFBRSxlQUFlO0NBQ3hCOztBQU1MLEFBQ0Msb0JBRG1CLENBQ25CLHNCQUFzQixDQUFDO0VBQ3RCLE9BQU8sRUFBRSxlQUFlO0NBQ3hCOztBR3JFRixVQUFVO0VBQ1QsV0FBVyxFQUFFLE9BQU87RUFDcEIsR0FBRyxFQUFFLDREQUFnRSxDQUFDLGtCQUFvQjtFQUMxRixXQUFXLEVBQUUsTUFBTTtFQUNuQixVQUFVLEVBQUUsTUFBTTs7O0FBR25CLFVBQVU7RUFDVCxXQUFXLEVBQUUsT0FBTztFQUNwQixHQUFHLEVBQUUseURBQTZELENBQUMsa0JBQW9CO0VBQ3ZGLFdBQVcsRUFBRSxJQUFJO0VBQ2pCLFVBQVUsRUFBRSxNQUFNOzs7QUFHbkIsVUFBVTtFQUNULFdBQVcsRUFBRSxPQUFPO0VBQ3BCLEdBQUcsRUFBRSwyREFBK0QsQ0FBQyxrQkFBb0I7RUFDekYsV0FBVyxFQUFFLE1BQU07RUFDbkIsVUFBVSxFQUFFLE1BQU07OztBQUduQixVQUFVO0VBQ1QsV0FBVyxFQUFFLE9BQU87RUFDcEIsR0FBRyxFQUFFLCtEQUFtRSxDQUFDLGtCQUFvQjtFQUM3RixXQUFXLEVBQUUsSUFBSTtFQUNqQixVQUFVLEVBQUUsTUFBTTs7O0FBR25CLFVBQVU7RUFDVCxXQUFXLEVBQUUsT0FBTztFQUNwQixHQUFHLEVBQUUsOERBQWtFLENBQUMsa0JBQW9CO0VBQzVGLFdBQVcsRUFBRSxHQUFHO0VBQ2hCLFVBQVUsRUFBRSxNQUFNOzs7QUFHbkIsVUFBVTtFQUNULFdBQVcsRUFBRSxPQUFPO0VBQ3BCLEdBQUcsRUFBRSxvRUFBd0UsQ0FBQyxrQkFBb0I7RUFDbEcsV0FBVyxFQUFFLEdBQUc7RUFDaEIsVUFBVSxFQUFFLE1BQU07OztBQUduQixVQUFVO0VBQ1QsV0FBVyxFQUFFLE9BQU87RUFDcEIsR0FBRyxFQUFFLDBEQUE4RCxDQUFDLGtCQUFvQjtFQUN4RixXQUFXLEVBQUUsR0FBRztFQUNoQixVQUFVLEVBQUUsTUFBTTs7O0FBR25CLFVBQVU7RUFDVCxXQUFXLEVBQUUsT0FBTztFQUNwQixHQUFHLEVBQUUsZ0VBQW9FLENBQUMsa0JBQW9CO0VBQzlGLFdBQVcsRUFBRSxHQUFHO0VBQ2hCLFVBQVUsRUFBRSxNQUFNOzs7QUFJbkIsVUFBVTtFQUNULFdBQVcsRUFBRSxVQUFVO0VBQ3ZCLEdBQUcsRUFBRSxrRUFBc0UsQ0FBQyxrQkFBb0I7RUFDaEcsV0FBVyxFQUFFLE1BQU07RUFDbkIsVUFBVSxFQUFFLE1BQU07OztBQUduQixVQUFVO0VBQ1QsV0FBVyxFQUFFLFVBQVU7RUFDdkIsR0FBRyxFQUFFLGlFQUFxRSxDQUFDLGtCQUFvQjtFQUMvRixXQUFXLEVBQUUsTUFBTTtFQUNuQixVQUFVLEVBQUUsTUFBTTs7O0FBR25CLFVBQVU7RUFDVCxXQUFXLEVBQUUsVUFBVTtFQUN2QixHQUFHLEVBQUUsaUVBQXFFLENBQUMsa0JBQW9CO0VBQy9GLFdBQVcsRUFBRSxHQUFHO0VBQ2hCLFVBQVUsRUFBRSxNQUFNOzs7QUFHbkIsVUFBVTtFQUNULFdBQVcsRUFBRSxVQUFVO0VBQ3ZCLEdBQUcsRUFBRSx1RUFBMkUsQ0FBQyxrQkFBb0I7RUFDckcsV0FBVyxFQUFFLEdBQUc7RUFDaEIsVUFBVSxFQUFFLE1BQU07OztBQUduQixVQUFVO0VBQ1QsV0FBVyxFQUFFLFVBQVU7RUFDdkIsR0FBRyxFQUFFLCtEQUFtRSxDQUFDLGtCQUFvQjtFQUM3RixXQUFXLEVBQUUsSUFBSTtFQUNqQixVQUFVLEVBQUUsTUFBTTs7O0FBR25CLFVBQVU7RUFDVCxXQUFXLEVBQUUsVUFBVTtFQUN2QixHQUFHLEVBQUUscUVBQXlFLENBQUMsa0JBQW9CO0VBQ25HLFdBQVcsRUFBRSxJQUFJO0VBQ2pCLFVBQVUsRUFBRSxNQUFNOzs7QUFHbkIsVUFBVTtFQUNULFdBQVcsRUFBRSxVQUFVO0VBQ3ZCLEdBQUcsRUFBRSxvRUFBd0UsQ0FBQyxrQkFBb0I7RUFDbEcsV0FBVyxFQUFFLEdBQUc7RUFDaEIsVUFBVSxFQUFFLE1BQU07OztBQUduQixVQUFVO0VBQ1QsV0FBVyxFQUFFLFVBQVU7RUFDdkIsR0FBRyxFQUFFLDBFQUE4RSxDQUFDLGtCQUFvQjtFQUN4RyxXQUFXLEVBQUUsR0FBRztFQUNoQixVQUFVLEVBQUUsTUFBTTs7O0FBR25CLFVBQVU7RUFDVCxXQUFXLEVBQUUsVUFBVTtFQUN2QixHQUFHLEVBQUUsZ0VBQW9FLENBQUMsa0JBQW9CO0VBQzlGLFdBQVcsRUFBRSxHQUFHO0VBQ2hCLFVBQVUsRUFBRSxNQUFNOzs7QUFHbkIsVUFBVTtFQUNULFdBQVcsRUFBRSxVQUFVO0VBQ3ZCLEdBQUcsRUFBRSxzRUFBMEUsQ0FBQyxrQkFBb0I7RUFDcEcsV0FBVyxFQUFFLEdBQUc7RUFDaEIsVUFBVSxFQUFFLE1BQU07OztBQUluQixVQUFVO0VBQ1QsV0FBVyxFQUFFLFNBQVM7RUFDdEIsR0FBRyxFQUFFLGdFQUFvRSxDQUFDLGtCQUFvQjtFQUM5RixXQUFXLEVBQUUsTUFBTTtFQUNuQixVQUFVLEVBQUUsTUFBTTs7O0FGbkluQixLQUFLO0VBQ0osTUFBTSxFQUFFLENBQUM7RUFDVCxPQUFPLEVBQUUsQ0FBQzs7O0FBR1gsQUFBQSxJQUFJLENBQUM7RUFDSixNQUFNLEVBQUUsS0FBaUMsQ0FBQyxDQUFDO0VBQ3hDLE9BQU8sRUFBRSxDQUFDO0VBQ1YsVUFBVSxFQUFFLElBQUk7RUFDaEIsVUFBVSxFQUFFLE9BQU87Q0FjdEI7O0FBbEJELEFBTUksSUFOQSxBQU1DLG1CQUFtQixDQUFDO0VBQ3ZCLE9BQU8sRUFBRSxJQUFJO0NBQ2I7O0FBUkYsQUFVQyxJQVZHLEFBVUYsMEJBQTBCLENBQUM7RUFDM0IsT0FBTyxFQUFFLElBQUk7Q0FDYjs7QUFaRixBQWNDLElBZEcsQ0FjSCxDQUFDLENBQUM7RUFDRCxhQUFhLEVBQUUsV0FBVztFQUMxQixjQUFjLEVBQUUsSUFBSTtDQUNwQjs7QUFHRixBQUFBLElBQUksRUFBRSxLQUFLLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDO0VBQ2hELFdBQVcsRUMxQkQsT0FBTyxFQUFFLFVBQVU7Q0QyQjlCOztBQUdELEFBQUEsZ0JBQWdCLENBQUM7RUFDYixRQUFRLEVBQUUsS0FBSztFQUNmLEdBQUcsRUFBRSxDQUFDO0VBQ04sSUFBSSxFQUFFLENBQUM7RUFDUCxLQUFLLEVBQUUsQ0FBQztFQUNSLE1BQU0sRUFBRSxDQUFDO0VBQ1QsS0FBSyxFQUFFLElBQUk7RUFDWCxNQUFNLEVBQUUsSUFBSTtFQUNaLE9BQU8sRUFBRSxFQUFFO0NBQ2Q7O0FBR0QsQUFBQSxxQkFBcUIsQ0FBQztFQUNsQixRQUFRLEVBQUUsS0FBSztFQUNmLEdBQUcsRUNoRE8sSUFBSTtFRGlEZCxJQUFJLEVDakRNLElBQUk7RURrRGQsS0FBSyxFQ2xESyxJQUFJO0VEbURkLE1BQU0sRUNuREksSUFBSTtFRG9EZCxPQUFPLEVBQUUsRUFBRTtDQWlEZDs7QUF2REQsQUFRQyxxQkFSb0IsQ0FRcEIsMEJBQTBCLENBQUM7RUFDMUIsUUFBUSxFQUFFLFFBQVE7RUFDbEIsS0FBSyxFQUFFLElBQUk7RUFDWCxNQUFNLEVBQUUsSUFBSTtDQUNaOztBQVpGLEFBY0MscUJBZG9CLENBY3BCLDBCQUEwQixDQUFDO0VBQzFCLFFBQVEsRUFBRSxRQUFRO0VBQ2xCLEdBQUcsRUFBRSxDQUFDO0VBQ04sSUFBSSxFQUFFLENBQUM7RUFDUCxLQUFLLEVBQUUsQ0FBQztFQUNSLE1BQU0sRUFBRSxDQUFDO0NBQ1Q7O0FBcEJGLEFBc0JDLHFCQXRCb0IsQ0FzQnBCLGlCQUFpQixDQUFDO0VBQ2pCLFFBQVEsRUFBRSxRQUFRO0VBQ2xCLEdBQUcsRUFBRSxDQUFDO0VBQ04sSUFBSSxFQUFFLENBQUM7RUFDUCxLQUFLLEVBQUUsQ0FBQztFQUNSLE1BQU0sRUFBRSxDQUFDO0VBQ1QsT0FBTyxFQUFFLEVBQUU7RUFFWCxnQkFBZ0IsRUFBRSxJQUFJO0VBQ3RCLGVBQWUsRUFBRSxTQUFTO0VBQzFCLGlCQUFpQixFQUFFLFNBQVM7Q0FzQjVCOztBQXRERixBQWtDRSxxQkFsQ21CLENBc0JwQixpQkFBaUIsQ0FZZixBQUFBLFdBQUMsQ0FBWSxPQUFPLEFBQW5CLEVBQXFCO0VBQ3RCLGdCQUFnQixFQUFFLDBEQUE0RDtFQUM5RSxJQUFJLEVBQUUsSUFBSTtFQUNWLEtBQUssRUFBRSxJQUFJO0VBQ1gsTUFBTSxFQUFFLEtBQUs7Q0FDYjs7QUF2Q0gsQUF5Q0UscUJBekNtQixDQXNCcEIsaUJBQWlCLENBbUJmLEFBQUEsV0FBQyxDQUFZLFFBQVEsQUFBcEIsRUFBc0I7RUFDdkIsZ0JBQWdCLEVBQUUsMERBQTREO0VBQzlFLElBQUksRUFBRSxLQUFLO0VBQ1gsS0FBSyxFQUFFLEtBQUs7RUFDWixNQUFNLEVBQUUsS0FBSztDQUNiOztBQTlDSCxBQWdERSxxQkFoRG1CLENBc0JwQixpQkFBaUIsQ0EwQmYsQUFBQSxXQUFDLENBQVksT0FBTyxBQUFuQixFQUFxQjtFQUN0QixnQkFBZ0IsRUFBRSwwREFBNEQ7RUFDOUUsSUFBSSxFQUFFLEtBQUs7RUFDWCxLQUFLLEVBQUUsS0FBSztFQUNaLE1BQU0sRUFBRSxLQUFLO0NBQ2I7O0FBS0gsQUFBQSw2QkFBNkIsQ0FBQztFQUM3QixRQUFRLEVBQUUsS0FBSztFQUNmLEdBQUcsRUMxR1UsSUFBSTtFRDJHZCxJQUFJLEVDM0dNLElBQUk7RUQ0R2QsS0FBSyxFQzVHSyxJQUFJO0VENkdkLE1BQU0sRUM3R0ksSUFBSTtFRDhHZCxPQUFPLEVBQUUsRUFBRTtDQUtkOztBQVhELEFBUUksNkJBUnlCLENBUXpCLHFCQUFxQixDQUFDO0VBQ3JCLFFBQVEsRUFBRSxRQUFRO0NBQ3JCOztBQUlGLEFBQUEsa0JBQWtCLENBQUM7RUFDbEIsTUFBTSxFQ3RIYSxLQUFJLENBRFYsSUFBSTtFRHdIakIsT0FBTyxFQ3ZIWSxJQUFJO0NEd0h2Qjs7QUFFRCxBQUFBLFFBQVEsQ0FBQztFQUNSLFVBQVUsRUFBRSxJQUFJO0NBaUNoQjs7QUFsQ0QsQUFHQyxRQUhPLENBR1AsS0FBSyxBQUFBLEtBQUssRUFIWCxRQUFRLENBR0ssRUFBRSxFQUhmLFFBQVEsQ0FHUyxFQUFFLEVBSG5CLFFBQVEsQ0FHYSxFQUFFLEVBSHZCLFFBQVEsQ0FHaUIsRUFBRSxFQUgzQixRQUFRLENBR3FCLEVBQUUsRUFIL0IsUUFBUSxDQUd5QixFQUFFLEVBSG5DLFFBQVEsQ0FHNkIsQ0FBQyxFQUh0QyxRQUFRLENBR2dDLEVBQUUsQUFBQSxJQUFLLENBQUEsT0FBTyxHQUh0RCxRQUFRLENBR2lELEVBQUUsRUFIM0QsUUFBUSxDQUdxRCxDQUFDLENBQUM7RUFDN0QsV0FBVyxFQUFFLEdBQUc7Q0FDaEI7O0FBTEYsQUFPQyxRQVBPLENBT1AsS0FBSyxDQUFDO0VBQ0wsS0FBSyxFQUFFLElBQUk7Q0FDWDs7QUFURixBQVdDLFFBWE8sQ0FXUCxFQUFFLEdBQUcsRUFBRSxDQUFDO0VBQ1AsZUFBZSxFQUFFLElBQUk7Q0FDckI7O0FBYkYsQUFlQyxRQWZPLENBZVAsRUFBRSxHQUFHLEVBQUUsQ0FBQztFQUNQLGVBQWUsRUFBRSxPQUFPO0NBQ3hCOztBQWpCRixBQW1CQyxRQW5CTyxDQW1CUCxhQUFhLENBQUMsWUFBWSxDQUFDO0VBQzFCLFdBQVcsRUFBRSxNQUFNO0NBU25COztBQTdCRixBQXNCRSxRQXRCTSxDQW1CUCxhQUFhLENBQUMsWUFBWSxDQUd6QixHQUFHLENBQUM7RUFDSCxPQUFPLEVBQUUsaUJBQWlCO0VBQzFCLEtBQUssRUFBRSxJQUFJO0VBQ1gsS0FBSyxFQUFFLElBQUk7RUFDWCxNQUFNLEVBQUUsSUFBSTtFQUNaLFlBQVksRUFBRSxHQUFHO0NBQ2pCOztBQTVCSCxBQStCQyxRQS9CTyxDQStCUCxvQkFBb0IsQ0FBQztFQUNwQixTQUFTLEVBQUUsSUFBSTtDQUNmOztBQUdGLEFBQUEsUUFBUSxDQUFDO0VBQ1IsS0FBSyxFQUFFLElBQUk7RUFDWCxNQUFNLEVBQUUsR0FBRztFQUNYLGdCQUFnQixFQUFFLE9BQU87RUFDekIsT0FBTyxFQUFFLElBQUk7RUFDYixNQUFNLEVBQUUsTUFBTTtDQUNkOztBQUVELEFBQUEsT0FBTyxDQUFDO0VBQ1AsT0FBTyxFQUFFLEtBQUs7RUFDZCxPQUFPLEVBQUUsQ0FBQztDQWFWOztBQWZELEFBSUMsT0FKTSxDQUlMLEFBQUEsVUFBQyxDQUFXLE1BQU0sQUFBakIsRUFBbUI7RUFDcEIsVUFBVSxFQUFFLElBQUk7Q0FDaEI7O0FBTkYsQUFRQyxPQVJNLENBUUwsQUFBQSxVQUFDLENBQVcsUUFBUSxBQUFuQixFQUFxQjtFQUN0QixVQUFVLEVBQUUsTUFBTTtDQUNsQjs7QUFWRixBQVlDLE9BWk0sQ0FZTCxBQUFBLFVBQUMsQ0FBVyxPQUFPLEFBQWxCLEVBQW9CO0VBQ3JCLFVBQVUsRUFBRSxLQUFLO0NBQ2pCOztDQUdGLEFBQUEsQUFBQSxTQUFDLENBQVUsT0FBTyxBQUFqQixFQUFtQixLQUFLLENBQUM7RUFDekIsVUFBVSxFQUFFLElBQUk7RUFDaEIsU0FBUyxFQUFFLEdBQUc7Q0FDZDs7Q0FFRCxBQUFBLEFBQUEsU0FBQyxDQUFVLFFBQVEsQUFBbEIsRUFBb0IsS0FBSyxDQUFDO0VBQzFCLFVBQVUsRUFBRSxLQUFLO0VBQ2pCLFNBQVMsRUFBRSxLQUFLO0NBQ2hCOztDQUVELEFBQUEsQUFBQSxTQUFDLENBQVUsT0FBTyxBQUFqQixFQUFtQixLQUFLLENBQUM7RUFDekIsVUFBVSxFQUFFLEtBQUs7RUFDakIsU0FBUyxFQUFFLEdBQUc7Q0FDZDs7Q0FFRCxBQUFBLEFBQUEsU0FBQyxDQUFVLE1BQU0sQUFBaEIsRUFBa0IsS0FBSyxDQUFDO0VBQ3hCLFVBQVUsRUFBRSxLQUFLO0VBQ2pCLFNBQVMsRUFBRSxJQUFJO0NBQ2Y7O0FBRUQsQUFDQyxLQURJLENBQ0gsQUFBQSxHQUFDLENBQUksRUFBRSxBQUFOLEVBQVE7RUFDVCxPQUFPLEVBQUUsZUFBZTtDQUN4Qjs7QUFHRixBQUVFLFVBRlEsQ0FDUixBQUFBLFNBQUMsQ0FBVSxNQUFNLEFBQWhCLEVBQ0QsZ0JBQWdCLENBQUM7RUFDaEIsT0FBTyxFQUFFLGVBQWU7Q0FDeEI7O0FBSkgsQUFRRSxVQVJRLENBT1IsQUFBQSxTQUFDLENBQVUsT0FBTyxBQUFqQixFQUNELGVBQWUsQ0FBQztFQUNmLE9BQU8sRUFBRSxlQUFlO0NBQ3hCOztBQUdELEFBQUQsZ0JBQU8sQ0FBQSxBQUFBLEdBQUMsQ0FBSSxFQUFFLEFBQU4sRUFBUTtFQUNmLE9BQU8sRUFBRSxJQUFJO0NBQ2I7O0FBRUEsQUFBRCxnQkFBTyxDQUFBLEFBQUEsU0FBQyxDQUFVLE9BQU8sQUFBakIsRUFBbUI7RUFDMUIsU0FBUyxFQUFFLEtBQUs7RUFDYixVQUFVLEVBQUUsSUFBSTtDQUNuQjs7QUFFQSxBQUFELGdCQUFPLENBQUEsQUFBQSxTQUFDLENBQVUsUUFBUSxBQUFsQixFQUFvQjtFQUMzQixTQUFTLEVBQUUsS0FBSztFQUNiLFVBQVUsRUFBRSxLQUFLO0NBQ3BCOztBQUVBLEFBQUQsZ0JBQU8sQ0FBQSxBQUFBLFNBQUMsQ0FBVSxPQUFPLEFBQWpCLEVBQW1CO0VBQzFCLFNBQVMsRUFBRSxLQUFLO0VBQ2IsVUFBVSxFQUFFLEtBQUs7Q0FDcEI7O0FBRUEsQUFBRCxnQkFBTyxDQUFBLEFBQUEsU0FBQyxDQUFVLE1BQU0sQUFBaEIsRUFBa0I7RUFDekIsU0FBUyxFQUFFLElBQUk7RUFDWixVQUFVLEVBQUUsS0FBSztDQUNwQjs7QUFHRixBQUFBLE9BQU8sQ0FBQztFQUNQLE9BQU8sRUFBRSxLQUFLO0NBQ2Q7O0FBRUQsQUFBQSxrQkFBa0IsQ0FBQztFQUNsQixPQUFPLEVBQUUsaUJBQWlCO0VBQzFCLGNBQWMsRUFBRSxHQUFHO0VBQ25CLFVBQVUsRUFBRSwyREFBMkQ7Q0FNdkU7O0FBVEQsQUFLQyxrQkFMaUIsQUFLaEIsZ0JBQWdCLENBQUM7RUFDakIsY0FBYyxFQUFFLElBQUk7RUFDcEIsYUFBYSxFQUFFLElBQUk7Q0FDbkI7O0FBR0YsQUFBQSxPQUFPLENBQUM7RUFDUCxPQUFPLEVBQUUsZUFBZTtDQUN4Qjs7QUFFRCxBQUFBLFNBQVMsQ0FBQztFQUNULEtBQUssRUFBRSxHQUFHO0NBQ1Y7O0FBRUQsQUFBQSxnQkFBZ0IsRUFBRSxpQkFBaUIsRUFBRSxjQUFjLEVBQUUsZ0JBQWdCLEVBQUUsZUFBZTtBQUN0RixpQkFBaUIsRUFBRSxZQUFZLEVBQUUsZ0JBQWdCLEVBQUUsY0FBYyxFQUFFLHFCQUFxQixFQUFFLGdCQUFnQixDQUFFO0VBQzNHLFVBQVUsRUFBRSxVQUFVO0NBQ3RCOztBQUVELEFBQUEsYUFBYSxFQUFFLGFBQWEsRUFBRSxpQkFBaUIsRUFBRSxXQUFXLEVBQUUsZ0JBQWdCLEVBQUUsZUFBZTtBQUMvRixlQUFlLEVBQUUsS0FBSyxFQUFFLGdCQUFnQixFQUFFLFNBQVMsRUFBRSxpQkFBaUI7QUFDdEUsYUFBYSxFQUFFLGFBQWEsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxlQUFlO0FBQ2pGLHVCQUF1QixFQUFFLE1BQU0sRUFBRSxnQkFBZ0IsRUFBRSxZQUFZLEVBQUUsb0JBQW9CLEVBQUUsWUFBWSxFQUFFLE9BQU8sRUFBRSxjQUFjLENBQUM7RUFDNUgsV0FBVyxFQUFFLE1BQU07Q0FDbkI7O0FHeFJELEFBQUEsNkJBQTZCLENBQUM7RUFDN0IsYUFBYSxFWFFFLENBQUM7Q1dpQmhCOztBQTFCRCxBQUdDLDZCQUg0QixHQUd4QixPQUFPLENBQUM7RUFDWCxVQUFVLEVBQUUsaUJBQWlCO0NBQzdCOztBQUxGLEFBT0ksNkJBUHlCLENBT3pCLDBCQUEwQixDQUFDO0VBQzFCLEdBQUcsRUFBRSxDQUFDO0VBQ04sS0FBSyxFQUFFLENBQUM7RUFDWCxJQUFJLEVBQUUsQ0FBQztFQUNQLE1BQU0sRUFBRSxLQUFLO0VBQ2IsYUFBYSxFWEhDLENBQUM7RVdJZixPQUFPLEVYUG9CLElBQUk7Q1dlNUI7O0FBckJMLEFBZ0JFLDZCQWhCMkIsQ0FPekIsMEJBQTBCLEdBU3hCLE9BQU87QUFoQmIsNkJBQTZCLENBT3pCLDBCQUEwQixHQVV4QixRQUFRO0FBakJkLDZCQUE2QixDQU96QiwwQkFBMEIsR0FXeEIsT0FBTyxDQUFDO0VBQ1gsVUFBVSxFQUFFLGlCQUFpQjtDQUM3Qjs7QUFwQkgsQUF1QkksNkJBdkJ5QixDQXVCekIsaUJBQWlCLENBQUM7RUFDcEIsTUFBTSxFQUFFLEtBQUs7Q0FDYjs7QUFJRixBQUlnQixRQUpSLENBQ0osS0FBSyxDQUNELEtBQUssQ0FDRCxFQUFFLEFBQUEsWUFBWSxDQUNWLEVBQUUsQ0FBQztFQUNDLFdBQVcsRUFBRSxDQUFDO0NBQ2pCOztBQU1qQixBQUFBLGtCQUFrQixDQUFDO0VBQ2xCLFVBQVUsRUFBRSxNQUFNO0VBQ2xCLE1BQU0sRUFBRSxDQUFDO0VBQ1QsVUFBVSxFQUFFLENBQUM7Q0FDYjs7QUFHRCxBQUFBLElBQUksQ0FBQztFQUNKLFVBQVUsRVg1Q0csSUFBSTtDVzZDakI7O0FBRUQsQUFBQSxPQUFPLENBQUM7RUFDUCxNQUFNLEVBQUUsT0FBd0IsQ0FBQyxVQUE0QztFQUM3RSxPQUFPLEVBQUUsQ0FBQyxDWGhEa0IsSUFBSTtDV3lEaEM7O0FBWEQsQUFJQyxPQUpNLENBSUwsQUFBQSxTQUFDLENBQVUsRUFBRSxBQUFaLENBQWEsSUFBSyxDQUFDLEdBQUcsQ0FBQSxBQUFBLEdBQUMsQ0FBSSxFQUFFLEFBQU4sR0FBVTtFQUNsQyxhQUFhLEVBQUUsQ0FBQztDQUNoQjs7QUFORixBQVFDLE9BUk0sQUFRTCxJQUFLLENBQUMsR0FBRyxDQUFBLEFBQUEsR0FBQyxDQUFJLEVBQUUsQUFBTixHQUFVO0VBQ3BCLGFBQWEsRUFBRSxDQUFDO0NBQ2hCOztBQUdGLEFBQ0Msa0JBRGlCLENBQ2pCLFFBQVEsQ0FBQztFQUNSLFVBQVUsRVg3RGlCLElBQUk7Q1c4RC9COztBWHRERixBQUFBLE9BQU8sQ0FBQztFQUNQLE1BQU0sRUFBRSxPQUFtQyxDQUFDLFVBQXVEO0NBQ25HOztBQUVELEFBQUEsa0JBQWtCLENBQUM7RUFDbEIsTUFBTSxFQWJzQixLQUFJLENBRG5CLElBQUk7RUFlakIsT0FBTyxFQWRxQixJQUFJLENBQ0YsSUFBSTtDQWNsQyJ9 */
