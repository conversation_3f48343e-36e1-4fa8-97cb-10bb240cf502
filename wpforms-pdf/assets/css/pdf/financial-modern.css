body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: bold;
  margin: 0 0 10px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 10px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

/* Base */
.body-inner {
  padding-top: 50px;
  padding-bottom: 50px;
}

.wrapper {
  max-width: 700px;
}

.wrapper-inner {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 40px 50px 50px 50px;
}

.header {
  text-align: center;
  padding: 0 0 50px 0;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  padding-top: 10px;
  font-size: 14px;
  line-height: 24px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 24px;
}

/* Tables */
.content .field-name {
  padding-top: 10px;
  padding-bottom: 10px;
  /* Repeater & Layout */
}

.content .field-name:not(.field-value) {
  font-size: 18px;
  line-height: 20.7px;
}

.content .field-name.field-repeater-name, .content .field-name.field-layout-name {
  font-size: 22px;
  padding-top: 30px;
  padding-bottom: 30px;
}

.content .field-value {
  padding-bottom: 30px;
}

.content .field-name.field-value {
  line-height: 24px;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.business-info {
  margin-top: 20px;
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
}

.business-info td {
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px !important;
  padding: 0;
  text-align: center !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.business-info td.divider {
  padding: 0;
  margin: 0;
  background: none !important;
  width: 0;
  opacity: 0.25;
}

.business-info td.business_address {
  padding-left: 25px;
  padding-right: 10px;
  max-width: 53%;
  width: 53%;
}

.business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
  max-width: 35%;
  width: 35%;
}

.business-info td.business_phone {
  padding-left: 10px;
  padding-right: 25px;
  max-width: 20%;
  width: 20%;
}

.header table {
  width: 100%;
}

.header table td {
  text-align: left;
}

.header table tr.divider {
  opacity: 1;
  margin: 0;
  background: none;
}

.header table tr.divider td {
  padding: 40px 0 40px 0;
  margin: 0;
}

.header table tr.divider .divider {
  margin: 0;
}

.logo-container[data-align="left"] {
  text-align: left;
}

.logo-container[data-align="center"] {
  text-align: center;
}

.logo-container[data-align="right"] {
  text-align: right;
}

.heading_1 {
  font-family: "Literata", serif;
  font-size: 48px;
  line-height: 36px;
  text-transform: uppercase;
  vertical-align: middle;
  color: #44403C;
}

.container-content {
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.subheading {
  font-family: "Literata", serif;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 2px;
  margin: 0 0 5px 0;
  text-transform: uppercase;
  padding-right: 40px;
  color: #FA7315;
}

.subcontent {
  font-size: 13px;
  line-height: 18px;
  padding-right: 40px;
  color: #78716C;
}

.subcontent p, .subcontent b, .subcontent strong, .subcontent div, .subcontent span, .subcontent a {
  font-size: 13px;
  line-height: 18px;
  color: #78716C;
  margin: 0;
  text-decoration: none;
}

.subcontent b, .subcontent strong {
  font-weight: bold;
  color: #44403C;
}

.wpforms-order-summary-preview th, .wpforms-order-summary-preview td {
  font-size: 14px;
  font-weight: normal;
  line-height: 14px;
  padding: 15px 20px;
  border-top: 2px solid #E7E5E4;
  border-bottom: 1px solid #E7E5E4;
}

.wpforms-order-summary-preview th {
  font-weight: bold;
}

.wpforms-order-summary-preview td {
  border-top-width: 1px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-label {
  text-align: left;
  padding-left: 0;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity {
  width: 120px;
  text-align: center;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-full {
  font-size: 14px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-price {
  width: 80px;
  text-align: left;
  padding-right: 0;
}

.wpforms-order-summary-preview .wpforms-order-summary-preview-total td {
  border-bottom-width: 0;
  font-weight: bold;
}

body {
  margin-top: 240px;
}

.container-background {
  top: 240px;
}

.logo-container {
  position: relative;
  height: auto;
}

.logo-container[data-size="small"] img.logo {
  max-height: 70px;
  max-width: 40%;
}

.logo-container[data-size="medium"] img.logo {
  max-height: 86px;
  max-width: 60%;
}

.logo-container[data-size="large"] img.logo {
  max-height: 100px;
  max-width: 80%;
}

.logo-container[data-size="full"] img.logo {
  max-height: 100px;
  max-width: 100%;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.header table {
  width: 100%;
  background: #ffffff;
}

.header td.logo-td {
  background: #ffffff;
  padding: 20px 0 40px 80px;
  max-width: 50% !important;
  width: 50% !important;
  vertical-align: bottom;
}

.header td.heading_container {
  background: #ffffff;
  padding: 70px 80px 40px 0;
  text-align: right;
  min-height: 190px;
}

.header .heading_1 {
  font-family: "Inter", sans-serif;
  font-size: 40px;
  line-height: 40px;
  color: #44403C;
  letter-spacing: 4px;
  margin-right: -4px;
  margin-bottom: 8px;
}

.header .invoice_number_heading, .header .invoice_number, .header .posted_date {
  font-size: 13px;
  line-height: 13px;
  margin: 0;
  padding: 0;
  color: #44403C;
}

.header .invoice_number_heading,
.header .invoice_number {
  font-weight: bold;
}

.header .invoice-info {
  margin-right: 0;
  line-height: 13px;
}

.header td.business-info-container {
  background: #44403c;
  padding: 15px 80px 70px 80px;
  text-align: center;
}

.header .business-info {
  background: none;
  margin-top: 0;
}

.subheading {
  font-family: "Inter", sans-serif;
  font-size: 12px;
  line-height: 16px;
  padding: 0;
  margin-bottom: 5px;
}

.subcontent {
  font-size: 13px;
  line-height: 20px;
  padding: 0;
}

.subcontent b, .subcontent strong {
  font-size: 14px;
  margin-bottom: 0px;
}

.subcontent p, .subcontent a, .subcontent span {
  font-size: 13px;
}

.billing-info {
  padding-right: 40px;
}

.container-content {
  padding: 40px;
  margin-top: 0;
}

.container-content .content-header {
  width: 100%;
  margin-bottom: 40px;
}

.container-content .billing-info {
  width: 60%;
}

.container-content .wpforms-order-summary-preview {
  border: 1px solid #E7E5E4;
}

.container-content .wpforms-order-summary-preview th {
  border-top-width: 1px;
}

.container-content .wpforms-order-summary-preview .wpforms-order-summary-item-label {
  padding-left: 20px;
}

.container-content .wpforms-order-summary-preview .wpforms-order-summary-item-price {
  padding-right: 20px;
}

.footer {
  margin-top: 40px;
}

.footer .divider {
  background-color: #F5F5F4;
  width: auto;
  margin-left: -40px;
  margin-right: -40px;
  opacity: 1;
}

.preview .container-content {
  height: calc( 100vh - 390px);
  overflow: hidden;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
