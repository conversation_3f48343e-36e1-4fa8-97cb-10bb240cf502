body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: bold;
  margin: 0 0 10px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 10px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

/* Base */
.body-inner {
  padding-top: 50px;
  padding-bottom: 50px;
}

.wrapper {
  max-width: 700px;
}

.wrapper-inner {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 40px 50px 50px 50px;
}

.header {
  text-align: center;
  padding: 0 0 50px 0;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  padding-top: 10px;
  font-size: 14px;
  line-height: 24px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 24px;
}

/* Tables */
.content .field-name {
  padding-top: 10px;
  padding-bottom: 10px;
  /* Repeater & Layout */
}

.content .field-name:not(.field-value) {
  font-size: 18px;
  line-height: 20.7px;
}

.content .field-name.field-repeater-name, .content .field-name.field-layout-name {
  font-size: 22px;
  padding-top: 30px;
  padding-bottom: 30px;
}

.content .field-value {
  padding-bottom: 30px;
}

.content .field-name.field-value {
  line-height: 24px;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.header[data-logo=""] + .divider, .header:has(img[src=""]) + .divider {
  display: none;
}

.preview .container-content {
  height: calc( 100vh - 210px);
  overflow: hidden;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
