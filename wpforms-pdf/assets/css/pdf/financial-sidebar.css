body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: bold;
  margin: 0 0 10px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 10px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

/* Base */
.body-inner {
  padding-top: 50px;
  padding-bottom: 50px;
}

.wrapper {
  max-width: 700px;
}

.wrapper-inner {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 40px 50px 50px 50px;
}

.header {
  text-align: center;
  padding: 0 0 50px 0;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  padding-top: 10px;
  font-size: 14px;
  line-height: 24px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 24px;
}

/* Tables */
.content .field-name {
  padding-top: 10px;
  padding-bottom: 10px;
  /* Repeater & Layout */
}

.content .field-name:not(.field-value) {
  font-size: 18px;
  line-height: 20.7px;
}

.content .field-name.field-repeater-name, .content .field-name.field-layout-name {
  font-size: 22px;
  padding-top: 30px;
  padding-bottom: 30px;
}

.content .field-value {
  padding-bottom: 30px;
}

.content .field-name.field-value {
  line-height: 24px;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.business-info {
  margin-top: 20px;
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
}

.business-info td {
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px !important;
  padding: 0;
  text-align: center !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.business-info td.divider {
  padding: 0;
  margin: 0;
  background: none !important;
  width: 0;
  opacity: 0.25;
}

.business-info td.business_address {
  padding-left: 25px;
  padding-right: 10px;
  max-width: 53%;
  width: 53%;
}

.business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
  max-width: 35%;
  width: 35%;
}

.business-info td.business_phone {
  padding-left: 10px;
  padding-right: 25px;
  max-width: 20%;
  width: 20%;
}

.header table {
  width: 100%;
}

.header table td {
  text-align: left;
}

.header table tr.divider {
  opacity: 1;
  margin: 0;
  background: none;
}

.header table tr.divider td {
  padding: 40px 0 40px 0;
  margin: 0;
}

.header table tr.divider .divider {
  margin: 0;
}

.logo-container[data-align="left"] {
  text-align: left;
}

.logo-container[data-align="center"] {
  text-align: center;
}

.logo-container[data-align="right"] {
  text-align: right;
}

.heading_1 {
  font-family: "Literata", serif;
  font-size: 48px;
  line-height: 36px;
  text-transform: uppercase;
  vertical-align: middle;
  color: #44403C;
}

.container-content {
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.subheading {
  font-family: "Literata", serif;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 2px;
  margin: 0 0 5px 0;
  text-transform: uppercase;
  padding-right: 40px;
  color: #FA7315;
}

.subcontent {
  font-size: 13px;
  line-height: 18px;
  padding-right: 40px;
  color: #78716C;
}

.subcontent p, .subcontent b, .subcontent strong, .subcontent div, .subcontent span, .subcontent a {
  font-size: 13px;
  line-height: 18px;
  color: #78716C;
  margin: 0;
  text-decoration: none;
}

.subcontent b, .subcontent strong {
  font-weight: bold;
  color: #44403C;
}

.wpforms-order-summary-preview th, .wpforms-order-summary-preview td {
  font-size: 14px;
  font-weight: normal;
  line-height: 14px;
  padding: 15px 20px;
  border-top: 2px solid #E7E5E4;
  border-bottom: 1px solid #E7E5E4;
}

.wpforms-order-summary-preview th {
  font-weight: bold;
}

.wpforms-order-summary-preview td {
  border-top-width: 1px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-label {
  text-align: left;
  padding-left: 0;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity {
  width: 120px;
  text-align: center;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-full {
  font-size: 14px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-price {
  width: 80px;
  text-align: left;
  padding-right: 0;
}

.wpforms-order-summary-preview .wpforms-order-summary-preview-total td {
  border-bottom-width: 0;
  font-weight: bold;
}

body {
  margin: 80px 0 0 0;
}

.container-background {
  top: 0;
  left: 270px;
  right: 0;
  bottom: 0;
  padding: 0;
  box-sizing: border-box;
}

.container-background .container-background-fill {
  right: 1px;
  bottom: 1px;
  border-top-right-radius: 11px;
  border-bottom-right-radius: 11px;
}

.container-background .container-background-fill.preview-highlight {
  outline-offset: -4px;
}

.container-background .container-shadow {
  background-size: 750px 100%;
}

.container-background .container-shadow[data-shadow="small"] {
  left: -5px;
}

.container-background .container-shadow[data-shadow="medium"] {
  left: -10px;
}

.container-background .container-shadow[data-shadow="large"] {
  left: -20px;
}

.container-background .sidebar {
  position: absolute;
  width: 270px;
  top: 0;
  left: 0;
  bottom: 0;
}

.container-sidebar {
  position: relative;
  margin: -80px 0 0 0;
  padding: 0 0 0 270px;
}

.container-content {
  height: 100vh;
  padding: 80px 40px;
  margin-left: 0;
  margin-right: 0;
  box-sizing: border-box;
}

.container-content.preview-highlight {
  outline-offset: -5px;
}

.header {
  position: absolute;
  width: 190px;
  top: 0;
  left: 0;
  padding: 80px 40px 0 40px;
}

.header[data-size="small"] .logo {
  max-height: 70px;
  max-width: 190px;
}

.header[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 190px;
}

.header[data-size="large"] .logo {
  max-height: 170px;
  max-width: 190px;
}

.header[data-size="full"] .logo {
  max-height: 360px;
  max-width: 190px;
}

.header .logo-container {
  margin: 0 0 40px 0;
}

.header .logo-container[data-logo=""] {
  margin: 0;
}

.header:has(img[src=""]) .logo-container {
  margin: 0;
}

.header .logo {
  max-width: 190px;
}

.header[data-align="left"] .business-info > div {
  text-align: left;
}

.header[data-align="center"] .business-info > div {
  text-align: center;
}

.header[data-align="right"] .business-info > div {
  text-align: right;
}

.header .business-info {
  margin: 0 0 40px 0;
}

.header .business-info div {
  text-align: left;
  font-size: 13px;
  line-height: 21px;
}

.header .business-info div a {
  color: inherit;
  font-size: inherit;
  text-decoration: none;
  line-height: inherit;
}

.header .business-info .business_name {
  font-size: 20px;
  font-weight: 700;
  line-height: 20px;
  margin-bottom: 5px;
}

.header .business-info .header_address, .header .business-info .header_address_2, .header .business-info .header_email, .header .business-info .header_phone {
  white-space: normal;
  word-break: break-word;
}

.header .heading_1 {
  font-family: "Inter", sans-serif;
  font-size: 40px;
  line-height: 40px;
  font-weight: 700;
  letter-spacing: 4px;
  margin: 0 -5px 20px 0;
  text-transform: uppercase;
  white-space: normal;
  word-wrap: break-word;
  max-height: 190px;
  overflow: hidden;
}

.header .sidebar-info {
  margin: 0 0 15px 0;
}

.header .paragraph_1 {
  font-size: 13px;
  line-height: 22px;
}

.subheading {
  font-family: "Inter", sans-serif;
  font-size: 12px;
  line-height: 12px;
  padding: 0;
  margin-bottom: 3px;
}

.subcontent {
  font-size: 13px;
  line-height: 13px;
  padding: 0;
}

.wpforms-order-summary-container {
  margin-left: -40px;
  margin-right: -40px;
}

.wpforms-order-summary-preview th {
  border-top-width: 1px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-label {
  padding-left: 40px;
}

.wpforms-order-summary-preview .wpforms-order-summary-item-price {
  padding-right: 40px;
}

.wpforms-order-summary-preview .wpforms-order-summary-placeholder td {
  padding-left: 40px;
}

.wpforms-order-summary-preview .wpforms-order-summary-preview-total .wpforms-order-summary-item-price {
  background-color: #FA7315;
  color: #ffffff;
  font-size: 16px;
}

.content {
  margin-top: 40px;
}

.content .divider {
  background-color: #F5F5F4;
  width: auto;
  margin-left: -40px;
  margin-right: -40px;
  opacity: 1;
}

.payment-info {
  margin-top: 40px;
}

.billing-info .subheading,
.payment-info .subheading,
.terms-info .subheading {
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 5px;
}

.billing-info .subcontent,
.payment-info .subcontent,
.terms-info .subcontent {
  padding: 0;
  line-height: 20px;
}

.billing-info .subcontent b, .billing-info .subcontent strong,
.payment-info .subcontent b,
.payment-info .subcontent strong,
.terms-info .subcontent b,
.terms-info .subcontent strong {
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 0px;
}

.billing-info .subcontent p, .billing-info .subcontent a, .billing-info .subcontent span,
.payment-info .subcontent p,
.payment-info .subcontent a,
.payment-info .subcontent span,
.terms-info .subcontent p,
.terms-info .subcontent a,
.terms-info .subcontent span {
  font-size: 13px;
  line-height: 20px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
