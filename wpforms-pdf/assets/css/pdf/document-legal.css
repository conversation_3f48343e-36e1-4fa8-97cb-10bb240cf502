body,
.body {
  height: 100% !important;
  margin: 0;
  padding: 0;
  width: 100% !important;
  min-width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

a img {
  border: none;
}

p {
  margin: 0 0 10px 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
}

table,
tr,
td {
  padding: 0;
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.ReadMsgBody,
.ExternalClass {
  width: 100%;
}

.ExternalClass {
  width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
  line-height: 100%;
}

table,
td {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
}

#outlook a {
  padding: 0;
}

img {
  -ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td,
th,
a {
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444444;
  word-wrap: normal;
  font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  font-weight: bold;
  margin: 0 0 10px 0;
  mso-line-height-rule: exactly;
  line-height: 1.3;
  line-height: 130%;
}

h1.normal,
h2.normal,
h3.normal,
h4.normal,
h5.normal,
h6.normal {
  font-weight: normal;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  mso-line-height-rule: exactly;
  line-height: 1.4;
  line-height: 140%;
}

p {
  margin: 0 0 10px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

p.large, p.text-large {
  font-size: 16px;
}

p.bold, p.text-bold {
  font-weight: 700;
}

p a {
  margin: inherit;
}

small {
  font-size: 80%;
}

center {
  width: 100%;
}

a {
  color: #e57722;
}

a:visited {
  color: #e57722;
}

a:hover, a:active {
  color: #904811;
}

h1 a,
h1 a:visited,
h2 a,
h2 a:visited,
h3 a,
h3 a:visited,
h4 a,
h4 a:visited,
h5 a,
h5 a:visited,
h6 a,
h6 a:visited {
  color: #e57722;
}

table.text-center,
th.text-center,
td.text-center,
h1.text-center,
h2.text-center,
h3.text-center,
h4.text-center,
h5.text-center,
h6.text-center,
p.text-center,
span.text-center {
  text-align: center;
}

table.text-left,
th.text-left,
td.text-left,
h1.text-left,
h2.text-left,
h3.text-left,
h4.text-left,
h5.text-left,
h6.text-left,
p.text-left,
span.text-left {
  text-align: left;
}

table.text-right,
th.text-right,
td.text-right,
h1.text-right,
h2.text-right,
h3.text-right,
h4.text-right,
h5.text-right,
h6.text-right,
p.text-right,
span.text-right {
  text-align: right;
}

table.primary,
th.primary,
td.primary,
h1.primary,
h2.primary,
h3.primary,
h4.primary,
h5.primary,
h6.primary,
p.primary,
span.primary {
  color: #333333;
}

table.orange,
th.orange,
td.orange,
h1.orange,
h2.orange,
h3.orange,
h4.orange,
h5.orange,
h6.orange,
p.orange,
span.orange {
  color: #e27730;
}

table.blue,
th.blue,
td.blue,
h1.blue,
h2.blue,
h3.blue,
h4.blue,
h5.blue,
h6.blue,
p.blue,
span.blue {
  color: #509fe2;
}

span.text-center {
  display: block;
  width: 100%;
  text-align: center;
}

ol,
ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

ol li,
ul li {
  list-style-type: decimal;
  padding-top: 5px;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0 !important;
}

/* Helper class for breaking long URLs. */
.break-all {
  word-break: break-all !important;
}

.break-all > a {
  word-break: break-all !important;
}

/* Base */
.body,
body {
  background-color: #e9eaec;
  text-align: center;
  padding: 0 25px 0 25px;
}

.container {
  margin: 0 auto 0 auto;
}

.header {
  line-height: 1;
}

.header .header-image {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
}

.header img {
  display: inline-block !important;
  max-height: 180px;
  vertical-align: middle;
}

.header-wrapper.dark-mode {
  display: none;
}

.content {
  /* Helper class for inline elements. */
}

.content a, .content p, .content pre {
  -ms-word-break: break-word;
  word-break: break-word;
}

.content pre {
  white-space: initial;
}

.content .inline {
  display: inline-block;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) {
  border-collapse: collapse;
  width: 100%;
}

.content .smart-tag table:not(.wpforms-order-summary-preview) td, .content .smart-tag table:not(.wpforms-order-summary-preview) th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.content td > *:last-child {
  margin-bottom: 0;
}

.footer {
  color: #999999;
}

.footer a {
  color: #999999;
  text-decoration: underline;
}

.footer a:hover {
  color: #333333;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

/* Buttons */
.button-link {
  border-radius: 3px;
  padding: 7px 15px;
  text-decoration: none;
}

/* Content */
.field-signature td.field-value {
  line-height: 1;
}

.field-rating td.field-value {
  line-height: 1;
}

tr:not(.smart-tag) > .field-value span {
  display: block;
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
  font-size: 22px;
}

/* File Upload */
.field-file-upload .field-value .file-icon {
  display: inline-block;
  vertical-align: middle;
}

/* RichText, Content */
.field-richtext .field-value:only-child, .field-content .field-value:only-child {
  display: inline-block;
  width: 100%;
}

.field-richtext p .alignleft,
.field-richtext li .alignleft, .field-content p .alignleft,
.field-content li .alignleft {
  float: left;
  margin-right: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext p .aligncenter,
.field-richtext li .aligncenter, .field-content p .aligncenter,
.field-content li .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.field-richtext p .alignright,
.field-richtext li .alignright, .field-content p .alignright,
.field-content li .alignright {
  float: right;
  margin-left: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.field-richtext table, .field-content table {
  border-collapse: collapse;
  width: 100%;
}

.field-richtext table td, .field-richtext table th, .field-content table td, .field-content table th {
  border: 1px solid currentColor;
  padding: 5px !important;
}

.field-rating .field-value {
  line-height: 1.3 !important;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.field-payment-total .wpforms-order-summary-container *,
.smart-tag .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full,
  .smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.field-payment-total table.wpforms-order-summary-preview,
.smart-tag table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.field-payment-total table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td, .field-payment-total table.wpforms-order-summary-preview tr th,
.smart-tag table.wpforms-order-summary-preview tr td,
.smart-tag table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .field-payment-total table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.smart-tag table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.field-payment-total table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.smart-tag table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.field-payment-total .wpforms-order-summary-container,
.smart-tag .wpforms-order-summary-container {
  max-width: 100%;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview caption,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-placeholder-hidden,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.smart-tag .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #d63638 !important;
}

.wpforms-layout-table > td {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row {
  width: 100%;
}

.wpforms-layout-table .wpforms-layout-table-row .field-value {
  padding-bottom: 25px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td {
  padding-right: 20px;
}

.wpforms-layout-table .wpforms-layout-table-row > tr > td:last-child {
  padding-right: 0;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:not(.wpforms-first-row) td.field-name {
  display: none;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row .field-value {
  padding-bottom: 15px;
}

.wpforms-layout-table-display-rows .wpforms-layout-table-row:last-child .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table-display-blocks .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value, .wpforms-layout-table-display-columns .wpforms-layout-table-row tr:last-child .wpforms-layout-table-cell .field-value {
  padding-bottom: 0;
}

.wpforms-layout-table .wpforms-layout-table-cell td {
  border: 0 !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-payment-total, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-payment-total {
  display: block !important;
}

.wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-25 .field-payment-total .wpforms-order-summary-preview, .wpforms-layout-table .wpforms-layout-table-cell.wpforms-width-33 .field-payment-total .wpforms-order-summary-preview {
  display: none !important;
}

.field-payment-total .wpforms-payment-total {
  display: none !important;
}

/* Base */
.body-inner {
  padding-top: 50px;
  padding-bottom: 50px;
}

.wrapper {
  max-width: 700px;
}

.wrapper-inner {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 40px 50px 50px 50px;
}

.header {
  text-align: center;
  padding: 0 0 50px 0;
}

.header .header-image {
  /* This is needed to center the logo in Outlook. */
  margin: 0 auto 0 auto;
}

.footer {
  padding-top: 10px;
  font-size: 14px;
  line-height: 24px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
  line-height: 24px;
}

/* Tables */
.content .field-name {
  padding-top: 10px;
  padding-bottom: 10px;
  /* Repeater & Layout */
}

.content .field-name:not(.field-value) {
  font-size: 18px;
  line-height: 20.7px;
}

.content .field-name.field-repeater-name, .content .field-name.field-layout-name {
  font-size: 22px;
  padding-top: 30px;
  padding-bottom: 30px;
}

.content .field-value {
  padding-bottom: 30px;
}

.content .field-name.field-value {
  line-height: 24px;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Inter/Inter-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Literata';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Literata/Literata-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Lobster';
  src: url("{WPFORMS_PDF_URL}assets/fonts/Lobster/Lobster-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@page {
  margin: 0;
  padding: 0;
}

body {
  margin: 105px 0;
  padding: 0;
  text-align: left;
  background: #ffffff;
}

body::-webkit-scrollbar {
  display: none;
}

body::-webkit-scrollbar-button {
  display: none;
}

body * {
  outline-color: transparent;
  outline-offset: 12px;
}

body, table, h1, h2, h3, h4, h5, h6, p, td, th, a {
  font-family: "Inter", sans-serif;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container-background {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background .container-background-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.container-background .container-background-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.container-background .container-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.container-background .container-shadow[data-shadow="small"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-sm.png");
  left: -5px;
  right: -5px;
  bottom: -10px;
}

.container-background .container-shadow[data-shadow="medium"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-md.png");
  left: -10px;
  right: -10px;
  bottom: -20px;
}

.container-background .container-shadow[data-shadow="large"] {
  background-image: url("{WPFORMS_PDF_URL}assets/images/shadow/shadow-lg.png");
  left: -20px;
  right: -20px;
  bottom: -35px;
}

.container-background-wrapper {
  position: fixed;
  top: 45px;
  left: 45px;
  right: 45px;
  bottom: 45px;
  z-index: -1;
}

.container-background-wrapper .container-background {
  position: relative;
}

.container-content {
  margin: -60px 45px;
  padding: 60px;
}

.content {
  min-height: 30px;
}

.content table.body, .content h1, .content h2, .content h3, .content h4, .content h5, .content h6, .content p, .content td:not(.header), .content th, .content a {
  line-height: 1.3;
}

.content table {
  width: 100%;
}

.content ul > li {
  list-style-type: disc;
}

.content ol > li {
  list-style-type: decimal;
}

.content .field-rating .field-value {
  white-space: nowrap;
}

.content .field-rating .field-value img {
  display: inline !important;
  float: none;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.content .field-repeater-name {
  font-size: 18px;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #44403C;
  opacity: 0.15;
  margin: 40px 0;
}

.header {
  display: block;
  padding: 0;
}

.header[data-align="left"] {
  text-align: left;
}

.header[data-align="center"] {
  text-align: center;
}

.header[data-align="right"] {
  text-align: right;
}

[data-size="small"] .logo {
  max-height: 70px;
  max-width: 33%;
}

[data-size="medium"] .logo {
  max-height: 120px;
  max-width: 340px;
}

[data-size="large"] .logo {
  max-height: 170px;
  max-width: 66%;
}

[data-size="full"] .logo {
  max-height: 360px;
  max-width: 100%;
}

.logo[src=""] {
  display: none !important;
}

.signature[data-type="text"] .signature_image {
  display: none !important;
}

.signature[data-type="image"] .signature_text {
  display: none !important;
}

.signature_image[src=""] {
  display: none;
}

.signature_image[data-size="small"] {
  max-width: 140px;
  max-height: 70px;
}

.signature_image[data-size="medium"] {
  max-width: 180px;
  max-height: 100px;
}

.signature_image[data-size="large"] {
  max-width: 220px;
  max-height: 130px;
}

.signature_image[data-size="full"] {
  max-width: 100%;
  max-height: 300px;
}

.footer {
  display: block;
}

.preview-highlight {
  outline: 4px solid #D63638;
  outline-offset: 6px;
  transition: outline-color 250ms ease-out, outline-offset 250ms ease-out;
}

.preview-highlight.page-background {
  outline-offset: -4px;
  border-radius: 10px;
}

.hidden {
  display: none !important;
}

.width-50 {
  width: 50%;
}

.billing_content, .business_address, .business_name, .details_content, .header_address,
.header_address_2, .paragraph_1, .payment_content, .terms_content, .signature_subheading, .date_subheading {
  word-break: break-word;
}

.header_email, .header_phone, .badge_subheading, .badge_year, .billing_heading, .business_email,
.business_phone, .date, .details_heading, .due_date, .due_date_heading,
.header_email, .header_phone, .heading_1, .heading_2, .heading_3, .invoice_number,
.invoice_number_heading, .cname, .payment_heading, .posted_date, .posted_date_heading, .tax_heading, .tax_id, .terms_heading {
  white-space: nowrap;
}

.container-content {
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.container-content .header .business-info {
  margin-top: 20px;
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
}

.container-content .header .business-info td {
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px;
  padding: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

.container-content .header .business-info td.divider {
  padding: 0;
  margin: 0;
  background: none;
  width: 0px;
  opacity: 0.25;
}

.container-content .header .business-info td.business_address {
  padding-left: 25px;
  padding-right: 10px;
  max-width: 53%;
  width: 53%;
}

.container-content .header .business-info td.business_email {
  padding-left: 10px;
  padding-right: 10px;
  max-width: 35%;
  width: 35%;
}

.container-content .header .business-info td.business_phone {
  padding-left: 10px;
  padding-right: 25px;
  max-width: 20%;
  width: 20%;
}

.container-content .header[data-align="left"] .business-info .business_address {
  padding-left: 0;
}

.container-content .header[data-align="right"] .business-info {
  margin-left: auto;
}

.container-content .header[data-align="right"] .business-info .business_phone {
  padding-right: 0;
}

.container-content .header[data-logo=""] .business-info, .container-content .header:has(img[src=""]) .business-info {
  margin-top: 0;
}

.preview .container-content {
  height: calc( 100vh - 210px);
  overflow: hidden;
}

.container-content .content ul, .container-content .content ol {
  margin: 0;
}

.container-content .content ul li, .container-content .content ol li {
  padding: 0;
  margin: 0 0 30px 0;
}

.container-content .content p, .container-content .content h1, .container-content .content h2, .container-content .content h3, .container-content .content h4, .container-content .content h5, .container-content .content h6 {
  margin: 0 0 30px 0;
}

.container-content .footer {
  margin-top: 40px;
}

.container-content .footer table.signature-date-table {
  width: 100%;
}

.container-content .footer table.signature-date-table td {
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
}

.container-content .footer table.signature-date-table tr.divider {
  opacity: 1;
  margin: 0;
  background-color: transparent;
}

.container-content .footer table.signature-date-table tr.divider td {
  padding: 10px 15% 0 15%;
  margin: 0;
}

.container-content .footer table.signature-date-table tr.divider .divider {
  margin: 0;
}

.container-content .footer table.signature-date-table td.signature {
  width: 50%;
}

.container-content .footer table.signature-date-table .signature_text {
  font-family: "Lobster", cursive;
  font-size: 40px;
  font-style: normal;
  font-weight: normal;
}

.container-content .footer table.signature-date-table .signature_image {
  margin: 0 auto;
  padding: 0;
}

.container-content .footer table.signature-date-table td.date {
  font-size: 14px;
  font-style: normal;
  font-weight: bold;
  vertical-align: bottom;
}

.container-content .footer table.signature-date-table .subheading td {
  text-transform: uppercase;
  padding-top: 10px;
}

.container-content .footer table.signature-date-table .signature_subheading {
  padding-right: 10px;
}

.container-content .footer table.signature-date-table .date_subheading {
  padding-left: 10px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
