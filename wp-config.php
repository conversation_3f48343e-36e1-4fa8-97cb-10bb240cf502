<?php

//define( 'ITSEC_ENCRYPTION_KEY', 'cU9wTndZLC86Onp6PTcsM1ooUElEL0xbITdDcS83JXQhMTw2NnNpRklZWUdsKlV4I0h9K1o5MldANGsyeCZMfQ==' );

define( 'WP_CACHE', true );

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the
 * installation. You don't have to use the web site, you can
 * copy this file to "wp-config.php" and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * MySQL settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link    https://codex.wordpress.org/Editing_wp-config.php
 *
 * @package WordPress
 */


// ** MySQL settings - You can get this info from your web host ** //

/** The name of the database for WordPress */


if ( $_SERVER['SERVER_NAME'] === 'wp.fpro.in.ua' ) {


	define( 'DB_NAME', 'wp' );
	define( 'DB_USER', 'wp' );
	define( 'DB_PASSWORD', '3R5u7X8e' );
	define( 'DB_HOST', 'localhost' );
	define( 'DB_CHARSET', 'utf8' );
	define( 'DB_COLLATE', '' );

} else {

	define( 'DB_USER', 'root' );
	define( 'DB_PASSWORD', 'local' );
	define( 'DB_HOST', '127.0.0.1' );
	define( 'DB_CHARSET', 'utf8' );
	define( 'DB_COLLATE', '' );

	if (
		isset( $_SERVER['HTTP_X_TESTING'] )
		|| ( isset( $_SERVER['HTTP_USER_AGENT'] ) && $_SERVER['HTTP_USER_AGENT'] === 'wp-browser' )
		|| getenv( 'WPBROWSER_HOST_REQUEST' )
	) {
		// Use the test database if the request comes from a test.
		define( 'DB_NAME', 'wp_codecept' );

	} else {
		// Else use the default one (insert your local DB name here).
		define( 'DB_NAME', 'wp' );
		//define( 'DB_NAME', 'wp_8824' );
	}
}

/**#@+
 * Authentication Unique Keys and Salts.
 *
 * Change these to different unique phrases!
 * You can generate these using the
 * {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service} You can change these at any
 * point in time to invalidate all existing cookies. This will force all users to have to log in again.
 *
 * @since 2.6.0
 */

define( 'AUTH_KEY', 'put your unique phrase here' );

define( 'SECURE_AUTH_KEY', 'put your unique phrase here' );

define( 'LOGGED_IN_KEY', 'put your unique phrase here' );

define( 'NONCE_KEY', 'put your unique phrase here' );

define( 'AUTH_SALT', 'put your unique phrase here' );

define( 'SECURE_AUTH_SALT', 'put your unique phrase here' );

define( 'LOGGED_IN_SALT', 'put your unique phrase here' );

define( 'NONCE_SALT', 'put your unique phrase here' );


/**#@-*/


/**
 * WordPress Database Table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */

$table_prefix = 'wp_';


/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the Codex.
 *
 * @link https://codex.wordpress.org/Debugging_in_WordPress
 */


const WP_DISABLE_FATAL_ERROR_HANDLER = false;
const WP_AUTO_UPDATE_CORE            = false;
const AUTOMATIC_UPDATER_DISABLED     = true;
const WP_MEMORY_LIMIT                = '512M';
const WP_MAX_MEMORY_LIMIT            = '512M';
const CONCATENATE_SCRIPTS            = false;
const AUTOSAVE_INTERVAL              = 300;
const WP_DEBUG                       = true;
const WP_DEBUG_DISPLAY               = false;
const WP_LOCAL_DEV                   = true;
const SCRIPT_DEBUG                   = false;
const FS_METHOD                      = 'direct';

const WPFORMS_DEBUG                  = true;
const WPFORMS_AI_DEBUG               = true;
const WPFORMS_CALCULATIONS_DEBUG     = true;
const WPFORMS_PDF_DEBUG              = true;


//define( 'SCRIPT_DEBUG', true );
//define( 'WPFORMS_DEV_TOOLS_MUTE_CORE_DEPRECATION_NOTICES', true );
//define( 'AUTOMATIC_UPDATER_DISABLED', true );
//define( 'WP_AUTO_UPDATE_CORE', false );

define( 'WPFORMS_DISABLE_ERROR_HANDLER', true );

define( 'WPFORMS_BRANCH_SWITCHER_LOG', false );

if ( $_SERVER['SERVER_NAME'] === 'wp.fpro.in.ua' ) {
	define( 'WPF_BRANCH_SWITCHER_TOKEN', "****************************************" );
	define( 'WPF_BRANCH_SWITCHER_USER', "AndriiSmith" );
	//define( 'FS_METHOD', 'ftpsockets' );
	define( 'FTP_HOST', 'localhost' );
	define( 'FTP_USER', 'h33930i' );
	define( 'FTP_PASS', 'B4z9N5j8' );
	define( 'FTP_SSL', false );
	define( 'FTP_BASE', '/www/wp.fpro.space/' );

	define( 'WPFORMS_LITE_CONNECT_STAGING', false );

} else {
	//define( 'WPFORMS_LICENSE_KEY', '650aa92f462b862d3f905a2a66bb52b5' ); // 650aa92f462b862d3f905a2a66bb52b5

}

define( 'WPFORMS_LITE_CONNECT_STAGING', false );

define( 'WPFORMS_DEBUG_LITE_CONNECT', [
	'id'  => '0mkqPSFcDD5BYnYJ0Ult',
	'key' => 'ac5d9cb67d31e6143d83934d0c01ad5ae184881b5dda0116017f89eacd45a0bb845f118dd71f8bf5756c1fb2cc102517da973f21943d8338b499424c6e69c9a222e0060c33e24240ea734ce4c23120ad7e9f418aaedb67b255f71b5f075df59f88db5355d7e516987f733f0aa37c92bbbf989038b435359fa0adff246e0a48a7',
] );



//define( 'WP_CONTENT_DIR', 'C:\www\wp.local\wp-content' );
define( 'WP_ENVIRONMENT_TYPE', 'production' );

define( 'WPFORMS_PAYPAL_COMMERCE_LOCAL_CONNECT_SERVER', true );
define( 'WPFORMS_PAYPAL_COMMERCE_SANDBOX_CLIENT_ID', 'ASn9mhp_WBAOiGZHjiXSaPQP7Z6T0pPkjziauoree6a4XkKSd32193Po-PIp7foAqVB5Jrl0YBaAwSfU' );
define( 'WPFORMS_PAYPAL_COMMERCE_SANDBOX_CLIENT_SECRET', 'EFFbXSiyKs-d1A4-EEp2L_SshSzbbf6JAHbe8-Z3rjv_vo50dZ4YOEP44WgfKnppHAeEE-dR-u5AzcPC' );
define( 'WPFORMS_PAYPAL_COMMERCE_SANDBOX_PARTNER_MERCHANT_ID', 'D774DNR9X7NLS' );

define( 'WPFORMS_GOOGLE_SHEETS_AMC', 'A0bgIKbiEJxrklkCGeNLZf4xDp7kunu3aoTiE1i7JxywA3hRb70cvWYpFusfTwX3mLd5TqNmjLKFk4y8biqidqA3jxPoesCczxPV' );

//define( 'WPFORMS_IMAGINE_API_KEY', '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' );


ini_set( 'upload_max_size', '5M' );
ini_set( 'post_max_size', '5M' );
ini_set( 'max_execution_time', '1800' );
ini_set( 'serialize_precision', '17' );


//error_reporting(E_ALL); ini_set('display_errors', 1);
//define( 'UPLOADS', '' );
//define( '_SITE_ENV', 'andri' );
//define( 'WPFORMS_SKIP_CHALLENGE', true );
// define( 'DISALLOW_FILE_MODS', true );
/* That's all, stop editing! Happy blogging. */

const WPPX_DEBUG = true;


/** Absolute path to the WordPress directory. */

if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', dirname( __FILE__ ) . '/' );
}


/** Sets up WordPress vars and included files. */

require_once( ABSPATH . 'wp-settings.php' );

