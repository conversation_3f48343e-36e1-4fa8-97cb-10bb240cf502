# WPForms (wpforms and wpforms-* dirs) PHP Coding Standards

The main working GIT repository is located in wp-content/plugins directory
The WPForms core plugin directory is wp-content/plugins/wpforms
The WPForms addons are located in wp-content/plugins-wpforms-* directories

## Indentation and Formatting
- Use tabs instead of spaces for indentation at the beginning of the line.
- Add a period at the end of comment sentences.
- Follow WordPress PHP coding standards for spacing, brackets, and formatting.
- Use proper line spacing between methods and logical sections.

## Text Domain Usage
- Use the correct text domain for internationalization functions.
- For wpforms-* plugins, use 'wpforms-*' as the text domain.
- For wpforms core, use 'wpforms'.
- For wpforms pro components, use 'wpforms'.
- Example: `esc_html__( 'Text to translate', 'wpforms-pdf' )`.

## Documentation
- Add PHPDoc blocks for all classes, methods, and properties.
- Include @since tags with '{VERSION}' placeholder.
- Document parameters with proper types.
- Document return values with proper types.
- Add descriptive comments for complex logic.
- Comments should have dot at the end.
- Comments should be in English.

## Naming Conventions
- Use snake_case for function and method names.
- Use snake_case for variable names.
- Use PascalCase for class names.
- Use descriptive and meaningful names.

## Security Practices
- Use WordPress sanitization functions (wpforms_sanitize_*).
- Use WordPress escaping functions (wpforms_esc_*).
- Verify nonces for form submissions.
- Check capabilities with current_user_can().

## Code Structure
- Follow object-oriented programming principles.
- Use namespaces (e.g., WPFormsPDF\\).
- Keep methods focused on single responsibilities.
- Use WordPress hooks system (actions and filters) for extensibility.

## PHP Version Compatibility
- Ensure code is compatible with PHP 7.2+.
- Avoid using features not available in PHP 7.2.

## Error Handling
- Use proper error handling with try-catch blocks where appropriate.
- Use wpforms_log() for logging errors.
- Provide meaningful error messages.

## Core Principles
- Adhere to PHP and WordPress best practices for consistency and readability.
- Emphasize object-oriented programming (OOP) for better modularity.
- Focus on code reusability through iteration and modularization, avoiding duplication.
- Use descriptive and meaningful functions, variables, and file names.
- Follow existing project directory naming conventions.
- Use WordPress hooks (actions and filters) to extend functionality.
- Add explicit, descriptive comments to improve code clarity and maintainability.

## PHP/WordPress Coding Practices
- Utilize features of PHP 7.2+ where applicable.
- Follow WordPress PHP coding standards throughout the codebase.
- Leverage core WordPress functions and APIs wherever possible.
- Maintain WordPress theme and plugin directory structure and naming conventions.
- Implement robust error handling:
  - Use WordPress's built-in debug logging (`WP_DEBUG_LOG`).
  - Apply try-catch blocks for controlled exception handling.
- Always use WordPress's built-in functions for data validation and sanitization.
- Ensure secure form handling by verifying nonces in submissions.
- For database interactions:
  - Use WordPress's `$wpdb` abstraction layer.
  - Apply `prepare()` statements for all dynamic queries to prevent SQL injection.
  - Use the `dbDelta()` function to manage database schema changes.

## Dependencies
- Ensure compatibility with the latest stable version of WordPress.
- Use Composer for dependency management in advanced plugins or themes.

## WordPress Best Practices
- Use WordPress's user roles and capabilities to manage permissions.
- Apply the Transients API to cache data and optimize performance.
- Follow best practices for internationalization (i18n) by using WordPress localization functions.
- Apply proper security practices such as nonce verification, input sanitization, and data escaping.
- Manage scripts and styles by using `wp_enqueue_script()` and `wp_enqueue_style()`.
- Store configuration data securely using WordPress's Options API.

## Key Conventions
- Follow WordPress's plugin API to extend functionality in a modular and scalable manner.
- Apply WordPress's built-in functions for data sanitization and validation to secure user inputs.
- For custom queries, use `$wpdb` or `WP_Query` for database interactions.
- For AJAX requests, use `admin-ajax.php` or the WordPress REST API to handle backend requests.
- Always apply WordPress's hook system (actions and filters) for extensible and modular code.
- Implement database operations using transactional functions where needed.

## WPForms & Addons Specific Rules

### 1. Project Structure
- Each plugin/addon must reside in its own directory, named with a unique slug identifier (e.g., `wpforms-mailchimp`).
- Main plugin/addon file must match the directory slug and contain a WordPress plugin header.
- For complex plugins/addons, use subdirectories such as: `assets/`, `src/`, `includes/`, `admin/`, `languages/`, `templates/`, `tests/`, `vendor/`, `build/`.
- Do not store temporary, personal, or build files in the root of the Plugins directory.

### 2. Code Organization & Namespacing
- Use namespaces where appropriate and unique prefixes for functions/classes.
- Organize classes in `src/` and follow PSR-4 autoloading via Composer.
- Each class should be in a separate file, named in StudlyCaps.

### 3. Composer and Dependency Management
- Each plugin/addon must have its own `composer.json` for autoloading and dependencies.
- Do not commit `vendor/`, `vendor_prefixed/`, or `node_modules/` directories; add them to `.gitignore`.
- Install dependencies via Composer during CI/CD or local development only.

### 4. Git and File Ignore Rules
- Ensure `.gitignore` excludes all build, dependency, and system files:
  - `vendor/`, `vendor_prefixed/`, `node_modules/`, `build/`, and all system/binary/temp files.
  - Exclude scaffolding, test helpers, and any large media or archive files.
- Only commit source code, configuration, and documentation.

### 5. Security and Validation
- Always sanitize and escape user data using WordPress functions.
- Verify nonces in forms and AJAX requests.
- Use role and capability checks for access control.
- Never store passwords or sensitive data in plain text.

### 6. Database Handling
- Use `$wpdb` with `prepare()` for all database queries.
- Use `dbDelta()` exclusively for creating/updating tables.

### 7. Localization and Internationalization
- Wrap all user-facing text in `__()`, `_e()`, or `_n()` with a unique text domain.

### 8. Scripts and Styles
- Enqueue scripts and styles using `wp_enqueue_script()` and `wp_enqueue_style()`.
- Do not include assets directly in templates.
- Use `wp_localize_script()` to pass data to scripts.
- For styles always use .scss files. For new features create new .scss file.
- Do not try to minify CSS or JS files, they are processed by gulp tasks.

#### Javascript code rules:
- follow the script structure of wpforms.js
- Use `const` and `let` instead of `var`.
- Do not use `eval()`, `alert()`
- Use `console.log()` for debugging only when requested.
- For string processing use ${} syntax. Example: `var message = 'Hello, ${name}!';`
- Avoid repetition of jQuery function $( selector ), always store it in a constant.
- Use chained method calls, e.g. `$(selector).addClass('active').css('color', 'red');`.
- Use method shorthand syntax, e.g. `{ coolMethod() {} }` instead of `{ coolMethod: function() {} }`.
- Use arrow functions, e.g. `() => {}` only when needed.

### 9. Caching and Performance
- Use the Transients API for caching.
- Avoid unnecessary database queries inside loops.

### 10. Testing and Documentation
- Each plugin/addon must have a `README.md` with description, installation, usage, and changelog.
- Place all tests in the `tests/` directory; include unit/integration tests where possible.
- Document all public classes and methods with PHPDoc comments.

### 11. Automation and CI/CD
- The build system based on the gulp scripts
- Use tools like `phpcs`, `eslint` for code quality and automation.
- Follow `.editorconfig` and `.gitignore` rules strictly.

### 12. General Best Practices
- Follow WordPress and WPForms coding, security, and architectural best practices.
- Use WordPress hooks (actions and filters) for extensibility.
- Organize code for clarity, modularity, and maintainability.
- Regularly update dependencies and codebase for compatibility and security.

---

**These unified rules ensure a clean, maintainable, secure, and scalable codebase for all plugins and addons within the `wp-content/plugins` directory, with a focus on the WPForms ecosystem and strict adherence to WordPress standards.**
