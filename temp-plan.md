# WPForms QR Code - Implementation Plan

## Phase 1: Project Setup and Foundation

### 1. Initial Setup from Scaffold
- [x] Copy all directories and files from `wpforms-addon-scaffold` to `wpforms-qr-code`
- [x] This will provide the basic structure and boilerplate code for the addon
- [x] All subsequent development will be done by modifying these copied files

### 2. Initial Plugin Structure Modifications
- [x] Rename the main plugin file from `wpforms-addon-scaffold.php` to `wpforms-qr-code.php`
- [x] Update the plugin header information in the main file:
  - [x] Plugin Name: WPForms QR Code
  - [x] Description: Automatically generates QR codes for form entries
  - [x] Text Domain: wpforms-qr-code
- [x] Update namespace from `WPFormsAddonScaffold` to `WPFormsQRCode` throughout the codebase
- [x] Set up constants for:
  - [x] `WPFORMS_QR_CODE_VERSION`
  - [x] `WPFORMS_QR_CODE_FILE`
  - [x] `WPFORMS_QR_CODE_URL`
  - [x] `WPFORMS_QR_CODE_DIR`
- [x] Update the main plugin class in `src/Plugin.php` with QR code specific initialization

### 3. Directory Structure Setup (Based on Scaffold)
- [x] Create the following directory structure:
  - [x] `src/` - For main plugin classes
  - [x] `assets/` - For CSS, JS, and image files
  - [x] `languages/` - For translation files
  - [x] `templates/` - For HTML templates
  - [x] `vendor/` - For dependencies (managed by Composer)

### 4. Composer Configuration
- [x] Create `composer.json` with:
  - [x] PSR-4 autoloading for `WPFormsQRCode` namespace
  - [x] PHP version requirements (7.2+)
  - [x] QR code library dependency
- [x] Set up proper dependency management
- [x] Create initial CHANGELOG.md file

### 5. QR Code Library Integration
- [x] Research and evaluate PHP QR code libraries (options: endroid/qr-code, chillerlan/php-qr-code)
- [x] Add the selected library as a dependency in composer.json
- [x] Create a wrapper class `src/QRCode/Generator.php` to handle QR code generation
- [x] Implement methods for generating QR codes with configurable parameters

## Phase 2: Core Functionality Implementation

### 6. QR Code Generation System
- [x] Create `src/QRCode/Generator.php` class to handle QR code generation
- [x] Implement methods to:
  - [x] Generate QR codes based on Entry ID
  - [x] Create the URL format for admin entry page
  - [x] Save QR codes to the filesystem
  - [x] Handle error cases and exceptions

### 7. Storage System
- [x] Create the upload directory structure at `wp-content/uploads/wpforms/qr-codes/`
- [x] Implement file naming convention (e.g., `form-{form_id}-entry-{entry_id}.png`)
- [x] Create `src/QRCode/Meta.php` class to handle entry meta storage
- [x] Add database integration to associate QR codes with entries
  - [x] Use the WPForms Entry Meta system via `wpforms()->get('entry_meta')->add()` method
  - [x] Store QR code data with the following parameters:
    - [x] `entry_id`: ID of the form entry
    - [x] `form_id`: ID of the form
    - [x] `type`: 'qr-code' (custom type for identification)
    - [x] `data`: Path to the QR code image relative to uploads directory
  - [x] Retrieve QR code data using `wpforms()->get('entry_meta')->get_meta()` with appropriate filters
  - [x] Add hooks for other plugins to access or modify QR code data
- [x] Create cleanup routines for orphaned QR code files

### 8. Entry Integration
- [x] Create `src/QRCode/Entry.php` class to handle entry integration
- [x] Hook into WPForms entry creation process
  - [x] Use `wpforms_process_entry_saved` action hook (priority 10) to generate QR code after entry is saved
  - [x] Parameters: `$fields`, `$entry`, `$form_data`, `$entry_id`, `$payment_id`
- [x] Generate QR code when an entry is created
  - [x] Store QR code data in entry meta using `wpforms()->get('entry_meta')->add()` method
  - [x] Use type 'qr-code' for easy identification and filtering
  - [x] Implement hooks for extensibility:
    - [x] `wpforms_qrcode_before_generate` - Before QR code generation
    - [x] `wpforms_qrcode_after_generate` - After QR code generation
    - [x] `wpforms_qrcode_image_path` - Filter the QR code image path
    - [x] `wpforms_qrcode_content` - Filter the QR code content (URL)
- [x] Add QR code display to admin entry detail page
  - [x] Use `wpforms_entry_details_content` action hook to add QR code to entry details page
  - [x] Create a custom metabox to display the QR code in the admin entry view

### 9. Settings Panel
- [x] Create `src/Builder/Settings.php` class to handle settings
- [x] Implement a "QR Code" tab in the form builder Settings panel
- [x] Add toggle options for:
  - [x] Enable QR Code in Notifications
  - [x] Enable QR Code in Confirmation
- [x] Add settings save/load functionality
- [x] Set default state for both toggles to "enabled"

### 10. Admin UI Integration
- [x] Create `src/Admin/Entry.php` class to handle admin UI integration
- [x] Add QR code display to the entry details page in admin
- [x] Create a metabox in the sidebar for the QR code. Locate it under the "Entry Details" section.
- [x] Add the "Verify" button under the QR code. Click on that button should update the entry status to "Verified". Store the status in entry meta.
  - [x] If the status is verified change the button to "Reset" and display the notice that the entry has been verified.

## Phase 3: User Interface and Settings

### 11. Confirmation Page Integration
- [x] Create `src/Frontend/Confirmation.php` class to handle confirmation page integration
- [x] Add QR code to confirmation page
  - [x] Use `wpforms_frontend_confirmation_message_after` action hook (priority 10) to add QR code after confirmation message
  - [x] Parameters: `$confirmation`, `$form_data`, `$fields`, `$entry_id`
  - [x] Add conditional display based on form settings
  - [x] Implement filter hook `wpforms_qrcode_display_confirmation` to control QR code display on confirmation page

### 12. Email Notification Integration
- [x] Create `src/Frontend/Notifications.php` class to handle email notification integration
- [x] Add QR code to email notifications
  - [x] Use `wpforms_email_message` filter hook (priority 10) to add QR code to email message
  - [x] Parameters: `$message`, `$email_data`, `$form_data`, `$entry`
  - [x] Add conditional display based on form settings
  - [x] Implement filter hook `wpforms_qrcode_display_email` to control QR code display in email notifications
- [x] Add QR code as email attachment
  - [x] Use `wpforms_email_attachments` filter hook (priority 10) to add QR code as attachment
  - [x] Parameters: `$attachments`, `$email_data`, `$form_data`, `$entry`
  - [x] Add conditional attachment based on form settings
  - [x] Implement filter hook `wpforms_qrcode_attach_email` to control QR code attachment in email notifications

### 13. Frontend Assets
- [x] Create CSS styles for QR code display on confirmation page
- [x] Implement responsive design for QR code display
- [x] Add print styles for QR code
- [x] Create JavaScript for QR code interaction (if needed)

### 14. Admin Assets
- [x] Create CSS styles for QR code display in admin
- [x] Implement responsive design for QR code display in admin
- [x] Add print styles for QR code in admin
- [x] Create JavaScript for QR code interaction in admin (verification, download, etc.)

## Phase 4: Testing and Documentation

### 15. Testing
- [x] Test QR code generation with various form configurations
- [x] Test QR code display on confirmation page with different confirmation types
- [x] Test QR code in email notifications with different email clients
- [x] Test QR code scanning with different devices and apps
- [x] Test QR code verification functionality
- [x] Test QR code download functionality
- [x] Test with different WordPress themes and plugins
- [x] Test with different PHP versions
- [x] Test with different browser versions

### 16. Documentation
- [x] Create user documentation for the addon
- [x] Create developer documentation for the addon
- [x] Add inline code comments and PHPDoc blocks
- [x] Create README.md file with installation and usage instructions
- [x] Create CHANGELOG.md file with version history

### 17. Final Review and Preparation
- [x] Review code for adherence to WordPress coding standards
- [x] Review code for adherence to WPForms coding standards
- [x] Review code for security best practices
- [x] Review code for performance optimization
- [x] Review code for accessibility
- [x] Review code for internationalization
- [x] Review code for backward compatibility
- [x] Prepare for release
